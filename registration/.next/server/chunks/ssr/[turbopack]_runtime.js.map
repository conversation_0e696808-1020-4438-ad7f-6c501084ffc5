{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts"], "sourcesContent": ["/**\n * This file contains runtime types and functions that are shared between all\n * TurboPack ECMAScript runtimes.\n *\n * It will be prepended to the runtime code of each runtime.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"./runtime-types.d.ts\" />\n\ntype EsmNamespaceObject = Record<string, any>\n\n// @ts-ignore Defined in `dev-base.ts`\ndeclare function getOrInstantiateModuleFromParent<M>(\n  id: ModuleId,\n  sourceModule: M\n): M\n\nconst REEXPORTED_OBJECTS = Symbol('reexported objects')\n\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>\n\ninterface ModuleContextEntry {\n  id: () => ModuleId\n  module: () => any\n}\n\ninterface ModuleContext {\n  // require call\n  (moduleId: ModuleId): Exports | EsmNamespaceObject\n\n  // async import call\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>\n\n  keys(): ModuleId[]\n\n  resolve(moduleId: ModuleId): ModuleId\n}\n\ntype GetOrInstantiateModuleFromParent<M extends Module> = (\n  moduleId: M['id'],\n  parentModule: M\n) => M\n\ndeclare function getOrInstantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): Module\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst toStringTag = typeof Symbol !== 'undefined' && Symbol.toStringTag\n\nfunction defineProp(\n  obj: any,\n  name: PropertyKey,\n  options: PropertyDescriptor & ThisType<any>\n) {\n  if (!hasOwnProperty.call(obj, name)) Object.defineProperty(obj, name, options)\n}\n\nfunction getOverwrittenModule(\n  moduleCache: ModuleCache<Module>,\n  id: ModuleId\n): Module {\n  let module = moduleCache[id]\n  if (!module) {\n    // This is invoked when a module is merged into another module, thus it wasn't invoked via\n    // instantiateModule and the cache entry wasn't created yet.\n    module = {\n      exports: {},\n      error: undefined,\n      loaded: false,\n      id,\n      namespaceObject: undefined,\n    }\n    moduleCache[id] = module\n  }\n  return module\n}\n\n/**\n * Adds the getters to the exports object.\n */\nfunction esm(\n  exports: Exports,\n  getters: Record<string, (() => any) | [() => any, (v: any) => void]>\n) {\n  defineProp(exports, '__esModule', { value: true })\n  if (toStringTag) defineProp(exports, toStringTag, { value: 'Module' })\n  for (const key in getters) {\n    const item = getters[key]\n    if (Array.isArray(item)) {\n      defineProp(exports, key, {\n        get: item[0],\n        set: item[1],\n        enumerable: true,\n      })\n    } else {\n      defineProp(exports, key, { get: item, enumerable: true })\n    }\n  }\n  Object.seal(exports)\n}\n\n/**\n * Makes the module an ESM with exports\n */\nfunction esmExport(\n  module: Module,\n  exports: Exports,\n  moduleCache: ModuleCache<Module>,\n  getters: Record<string, () => any>,\n  id: ModuleId | undefined\n) {\n  if (id != null) {\n    module = getOverwrittenModule(moduleCache, id)\n    exports = module.exports\n  }\n  module.namespaceObject = module.exports\n  esm(exports, getters)\n}\n\nfunction ensureDynamicExports(module: Module, exports: Exports) {\n  let reexportedObjects = module[REEXPORTED_OBJECTS]\n\n  if (!reexportedObjects) {\n    reexportedObjects = module[REEXPORTED_OBJECTS] = []\n    module.exports = module.namespaceObject = new Proxy(exports, {\n      get(target, prop) {\n        if (\n          hasOwnProperty.call(target, prop) ||\n          prop === 'default' ||\n          prop === '__esModule'\n        ) {\n          return Reflect.get(target, prop)\n        }\n        for (const obj of reexportedObjects!) {\n          const value = Reflect.get(obj, prop)\n          if (value !== undefined) return value\n        }\n        return undefined\n      },\n      ownKeys(target) {\n        const keys = Reflect.ownKeys(target)\n        for (const obj of reexportedObjects!) {\n          for (const key of Reflect.ownKeys(obj)) {\n            if (key !== 'default' && !keys.includes(key)) keys.push(key)\n          }\n        }\n        return keys\n      },\n    })\n  }\n}\n\n/**\n * Dynamically exports properties from an object\n */\nfunction dynamicExport(\n  module: Module,\n  exports: Exports,\n  moduleCache: ModuleCache<Module>,\n  object: Record<string, any>,\n  id: ModuleId | undefined\n) {\n  if (id != null) {\n    module = getOverwrittenModule(moduleCache, id)\n    exports = module.exports\n  }\n  ensureDynamicExports(module, exports)\n\n  if (typeof object === 'object' && object !== null) {\n    module[REEXPORTED_OBJECTS]!.push(object)\n  }\n}\n\nfunction exportValue(\n  module: Module,\n  moduleCache: ModuleCache<Module>,\n  value: any,\n  id: ModuleId | undefined\n) {\n  if (id != null) {\n    module = getOverwrittenModule(moduleCache, id)\n  }\n  module.exports = value\n}\n\nfunction exportNamespace(\n  module: Module,\n  moduleCache: ModuleCache<Module>,\n  namespace: any,\n  id: ModuleId | undefined\n) {\n  if (id != null) {\n    module = getOverwrittenModule(moduleCache, id)\n  }\n  module.exports = module.namespaceObject = namespace\n}\n\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\n  return () => obj[key]\n}\n\n/**\n * @returns prototype of the object\n */\nconst getProto: (obj: any) => any = Object.getPrototypeOf\n  ? (obj) => Object.getPrototypeOf(obj)\n  : (obj) => obj.__proto__\n\n/** Prototypes that are not expanded for exports */\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)]\n\n/**\n * @param raw\n * @param ns\n * @param allowExportDefault\n *   * `false`: will have the raw module as default export\n *   * `true`: will have the default property as default export\n */\nfunction interopEsm(\n  raw: Exports,\n  ns: EsmNamespaceObject,\n  allowExportDefault?: boolean\n) {\n  const getters: { [s: string]: () => any } = Object.create(null)\n  for (\n    let current = raw;\n    (typeof current === 'object' || typeof current === 'function') &&\n    !LEAF_PROTOTYPES.includes(current);\n    current = getProto(current)\n  ) {\n    for (const key of Object.getOwnPropertyNames(current)) {\n      getters[key] = createGetter(raw, key)\n    }\n  }\n\n  // this is not really correct\n  // we should set the `default` getter if the imported module is a `.cjs file`\n  if (!(allowExportDefault && 'default' in getters)) {\n    getters['default'] = () => raw\n  }\n\n  esm(ns, getters)\n  return ns\n}\n\nfunction createNS(raw: Module['exports']): EsmNamespaceObject {\n  if (typeof raw === 'function') {\n    return function (this: any, ...args: any[]) {\n      return raw.apply(this, args)\n    }\n  } else {\n    return Object.create(null)\n  }\n}\n\nfunction esmImport(\n  sourceModule: Module,\n  id: ModuleId\n): Exclude<Module['namespaceObject'], undefined> {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule)\n  if (module.error) throw module.error\n\n  // any ES module has to have `module.namespaceObject` defined.\n  if (module.namespaceObject) return module.namespaceObject\n\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\n  const raw = module.exports\n  return (module.namespaceObject = interopEsm(\n    raw,\n    createNS(raw),\n    raw && (raw as any).__esModule\n  ))\n}\n\n// Add a simple runtime require so that environments without one can still pass\n// `typeof require` CommonJS checks so that exports are correctly registered.\nconst runtimeRequire =\n  // @ts-ignore\n  typeof require === 'function'\n    ? // @ts-ignore\n      require\n    : function require() {\n        throw new Error('Unexpected use of runtime require')\n      }\n\nfunction commonJsRequire(sourceModule: Module, id: ModuleId): Exports {\n  const module = getOrInstantiateModuleFromParent(id, sourceModule)\n  if (module.error) throw module.error\n  return module.exports\n}\n\n/**\n * `require.context` and require/import expression runtime.\n */\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\n  function moduleContext(id: ModuleId): Exports {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].module()\n    }\n\n    const e = new Error(`Cannot find module '${id}'`)\n    ;(e as any).code = 'MODULE_NOT_FOUND'\n    throw e\n  }\n\n  moduleContext.keys = (): ModuleId[] => {\n    return Object.keys(map)\n  }\n\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].id()\n    }\n\n    const e = new Error(`Cannot find module '${id}'`)\n    ;(e as any).code = 'MODULE_NOT_FOUND'\n    throw e\n  }\n\n  moduleContext.import = async (id: ModuleId) => {\n    return await (moduleContext(id) as Promise<Exports>)\n  }\n\n  return moduleContext\n}\n\n/**\n * Returns the path of a chunk defined by its data.\n */\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\n  return typeof chunkData === 'string' ? chunkData : chunkData.path\n}\n\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\n  return (\n    maybePromise != null &&\n    typeof maybePromise === 'object' &&\n    'then' in maybePromise &&\n    typeof maybePromise.then === 'function'\n  )\n}\n\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\n  return turbopackQueues in obj\n}\n\nfunction createPromise<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason?: any) => void\n\n  const promise = new Promise<T>((res, rej) => {\n    reject = rej\n    resolve = res\n  })\n\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  }\n}\n\n// everything below is adapted from webpack\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\n\nconst turbopackQueues = Symbol('turbopack queues')\nconst turbopackExports = Symbol('turbopack exports')\nconst turbopackError = Symbol('turbopack error')\n\nconst enum QueueStatus {\n  Unknown = -1,\n  Unresolved = 0,\n  Resolved = 1,\n}\n\ntype AsyncQueueFn = (() => void) & { queueCount: number }\ntype AsyncQueue = AsyncQueueFn[] & {\n  status: QueueStatus\n}\n\nfunction resolveQueue(queue?: AsyncQueue) {\n  if (queue && queue.status !== QueueStatus.Resolved) {\n    queue.status = QueueStatus.Resolved\n    queue.forEach((fn) => fn.queueCount--)\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()))\n  }\n}\n\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>\n\ntype AsyncModuleExt = {\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void\n  [turbopackExports]: Exports\n  [turbopackError]?: any\n}\n\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt\n\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\n  return deps.map((dep): AsyncModuleExt => {\n    if (dep !== null && typeof dep === 'object') {\n      if (isAsyncModuleExt(dep)) return dep\n      if (isPromise(dep)) {\n        const queue: AsyncQueue = Object.assign([], {\n          status: QueueStatus.Unresolved,\n        })\n\n        const obj: AsyncModuleExt = {\n          [turbopackExports]: {},\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\n        }\n\n        dep.then(\n          (res) => {\n            obj[turbopackExports] = res\n            resolveQueue(queue)\n          },\n          (err) => {\n            obj[turbopackError] = err\n            resolveQueue(queue)\n          }\n        )\n\n        return obj\n      }\n    }\n\n    return {\n      [turbopackExports]: dep,\n      [turbopackQueues]: () => {},\n    }\n  })\n}\n\nfunction asyncModule(\n  module: Module,\n  body: (\n    handleAsyncDependencies: (\n      deps: Dep[]\n    ) => Exports[] | Promise<() => Exports[]>,\n    asyncResult: (err?: any) => void\n  ) => void,\n  hasAwait: boolean\n) {\n  const queue: AsyncQueue | undefined = hasAwait\n    ? Object.assign([], { status: QueueStatus.Unknown })\n    : undefined\n\n  const depQueues: Set<AsyncQueue> = new Set()\n\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>()\n\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\n    [turbopackExports]: module.exports,\n    [turbopackQueues]: (fn) => {\n      queue && fn(queue)\n      depQueues.forEach(fn)\n      promise['catch'](() => {})\n    },\n  } satisfies AsyncModuleExt)\n\n  const attributes: PropertyDescriptor = {\n    get(): any {\n      return promise\n    },\n    set(v: any) {\n      // Calling `esmExport` leads to this.\n      if (v !== promise) {\n        promise[turbopackExports] = v\n      }\n    },\n  }\n\n  Object.defineProperty(module, 'exports', attributes)\n  Object.defineProperty(module, 'namespaceObject', attributes)\n\n  function handleAsyncDependencies(deps: Dep[]) {\n    const currentDeps = wrapDeps(deps)\n\n    const getResult = () =>\n      currentDeps.map((d) => {\n        if (d[turbopackError]) throw d[turbopackError]\n        return d[turbopackExports]\n      })\n\n    const { promise, resolve } = createPromise<() => Exports[]>()\n\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\n      queueCount: 0,\n    })\n\n    function fnQueue(q: AsyncQueue) {\n      if (q !== queue && !depQueues.has(q)) {\n        depQueues.add(q)\n        if (q && q.status === QueueStatus.Unresolved) {\n          fn.queueCount++\n          q.push(fn)\n        }\n      }\n    }\n\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue))\n\n    return fn.queueCount ? promise : getResult()\n  }\n\n  function asyncResult(err?: any) {\n    if (err) {\n      reject((promise[turbopackError] = err))\n    } else {\n      resolve(promise[turbopackExports])\n    }\n\n    resolveQueue(queue)\n  }\n\n  body(handleAsyncDependencies, asyncResult)\n\n  if (queue && queue.status === QueueStatus.Unknown) {\n    queue.status = QueueStatus.Unresolved\n  }\n}\n\n/**\n * A pseudo \"fake\" URL object to resolve to its relative path.\n *\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\n * hydration mismatch.\n *\n * This is based on webpack's existing implementation:\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\n */\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\n  const realUrl = new URL(inputUrl, 'x:/')\n  const values: Record<string, any> = {}\n  for (const key in realUrl) values[key] = (realUrl as any)[key]\n  values.href = inputUrl\n  values.pathname = inputUrl.replace(/[?#].*/, '')\n  values.origin = values.protocol = ''\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl\n  for (const key in values)\n    Object.defineProperty(this, key, {\n      enumerable: true,\n      configurable: true,\n      value: values[key],\n    })\n}\n\nrelativeURL.prototype = URL.prototype\n\n/**\n * Utility function to ensure all variants of an enum are handled.\n */\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\n  throw new Error(`Invariant: ${computeMessage(never)}`)\n}\n\n/**\n * A stub function to make `require` available but non-functional in ESM.\n */\nfunction requireStub(_moduleId: ModuleId): never {\n  throw new Error('dynamic usage of require is not supported')\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,6CAA6C;AAU7C,MAAM,qBAAqB,OAAO;AA+BlC,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;AACtD,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,WAAW;AAEvE,SAAS,WACP,GAAQ,EACR,IAAiB,EACjB,OAA2C;IAE3C,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,OAAO,OAAO,cAAc,CAAC,KAAK,MAAM;AACxE;AAEA,SAAS,qBACP,WAAgC,EAChC,EAAY;IAEZ,IAAI,SAAS,WAAW,CAAC,GAAG;IAC5B,IAAI,CAAC,QAAQ;QACX,0FAA0F;QAC1F,4DAA4D;QAC5D,SAAS;YACP,SAAS,CAAC;YACV,OAAO;YACP,QAAQ;YACR;YACA,iBAAiB;QACnB;QACA,WAAW,CAAC,GAAG,GAAG;IACpB;IACA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,IACP,OAAgB,EAChB,OAAoE;IAEpE,WAAW,SAAS,cAAc;QAAE,OAAO;IAAK;IAChD,IAAI,aAAa,WAAW,SAAS,aAAa;QAAE,OAAO;IAAS;IACpE,IAAK,MAAM,OAAO,QAAS;QACzB,MAAM,OAAO,OAAO,CAAC,IAAI;QACzB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,WAAW,SAAS,KAAK;gBACvB,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,YAAY;YACd;QACF,OAAO;YACL,WAAW,SAAS,KAAK;gBAAE,KAAK;gBAAM,YAAY;YAAK;QACzD;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,UACP,MAAc,EACd,OAAgB,EAChB,WAAgC,EAChC,OAAkC,EAClC,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;QAC3C,UAAU,OAAO,OAAO;IAC1B;IACA,OAAO,eAAe,GAAG,OAAO,OAAO;IACvC,IAAI,SAAS;AACf;AAEA,SAAS,qBAAqB,MAAc,EAAE,OAAgB;IAC5D,IAAI,oBAAoB,MAAM,CAAC,mBAAmB;IAElD,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,MAAM,CAAC,mBAAmB,GAAG,EAAE;QACnD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG,IAAI,MAAM,SAAS;YAC3D,KAAI,MAAM,EAAE,IAAI;gBACd,IACE,eAAe,IAAI,CAAC,QAAQ,SAC5B,SAAS,aACT,SAAS,cACT;oBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;gBAC7B;gBACA,KAAK,MAAM,OAAO,kBAAoB;oBACpC,MAAM,QAAQ,QAAQ,GAAG,CAAC,KAAK;oBAC/B,IAAI,UAAU,WAAW,OAAO;gBAClC;gBACA,OAAO;YACT;YACA,SAAQ,MAAM;gBACZ,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,MAAM,OAAO,kBAAoB;oBACpC,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAM;wBACtC,IAAI,QAAQ,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC1D;gBACF;gBACA,OAAO;YACT;QACF;IACF;AACF;AAEA;;CAEC,GACD,SAAS,cACP,MAAc,EACd,OAAgB,EAChB,WAAgC,EAChC,MAA2B,EAC3B,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;QAC3C,UAAU,OAAO,OAAO;IAC1B;IACA,qBAAqB,QAAQ;IAE7B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;QACjD,MAAM,CAAC,mBAAmB,CAAE,IAAI,CAAC;IACnC;AACF;AAEA,SAAS,YACP,MAAc,EACd,WAAgC,EAChC,KAAU,EACV,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;IAC7C;IACA,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,gBACP,MAAc,EACd,WAAgC,EAChC,SAAc,EACd,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;IAC7C;IACA,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG;AAC5C;AAEA,SAAS,aAAa,GAAiC,EAAE,GAAoB;IAC3E,OAAO,IAAM,GAAG,CAAC,IAAI;AACvB;AAEA;;CAEC,GACD,MAAM,WAA8B,OAAO,cAAc,GACrD,CAAC,MAAQ,OAAO,cAAc,CAAC,OAC/B,CAAC,MAAQ,IAAI,SAAS;AAE1B,iDAAiD,GACjD,MAAM,kBAAkB;IAAC;IAAM,SAAS,CAAC;IAAI,SAAS,EAAE;IAAG,SAAS;CAAU;AAE9E;;;;;;CAMC,GACD,SAAS,WACP,GAAY,EACZ,EAAsB,EACtB,kBAA4B;IAE5B,MAAM,UAAsC,OAAO,MAAM,CAAC;IAC1D,IACE,IAAI,UAAU,KACd,CAAC,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU,KAC7D,CAAC,gBAAgB,QAAQ,CAAC,UAC1B,UAAU,SAAS,SACnB;QACA,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,SAAU;YACrD,OAAO,CAAC,IAAI,GAAG,aAAa,KAAK;QACnC;IACF;IAEA,6BAA6B;IAC7B,6EAA6E;IAC7E,IAAI,CAAC,CAAC,sBAAsB,aAAa,OAAO,GAAG;QACjD,OAAO,CAAC,UAAU,GAAG,IAAM;IAC7B;IAEA,IAAI,IAAI;IACR,OAAO;AACT;AAEA,SAAS,SAAS,GAAsB;IACtC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,SAAqB,GAAG,IAAW;YACxC,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACzB;IACF,OAAO;QACL,OAAO,OAAO,MAAM,CAAC;IACvB;AACF;AAEA,SAAS,UACP,YAAoB,EACpB,EAAY;IAEZ,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IAEpC,8DAA8D;IAC9D,IAAI,OAAO,eAAe,EAAE,OAAO,OAAO,eAAe;IAEzD,iGAAiG;IACjG,MAAM,MAAM,OAAO,OAAO;IAC1B,OAAQ,OAAO,eAAe,GAAG,WAC/B,KACA,SAAS,MACT,OAAO,AAAC,IAAY,UAAU;AAElC;AAEA,+EAA+E;AAC/E,6EAA6E;AAC7E,MAAM,iBACJ,aAAa;AACb,OAAO,YAAY,aAEf,UACA,SAAS;IACP,MAAM,IAAI,MAAM;AAClB;AAEN,SAAS,gBAAgB,YAAoB,EAAE,EAAY;IACzD,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IACpC,OAAO,OAAO,OAAO;AACvB;AAEA;;CAEC,GACD,SAAS,cAAc,GAAqB;IAC1C,SAAS,cAAc,EAAY;QACjC,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM;QACvB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,IAAI,GAAG;QACnB,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,cAAc,OAAO,GAAG,CAAC;QACvB,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,MAAM,GAAG,OAAO;QAC5B,OAAO,MAAO,cAAc;IAC9B;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,aAAa,SAAoB;IACxC,OAAO,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;AACnE;AAEA,SAAS,UAAmB,YAAiB;IAC3C,OACE,gBAAgB,QAChB,OAAO,iBAAiB,YACxB,UAAU,gBACV,OAAO,aAAa,IAAI,KAAK;AAEjC;AAEA,SAAS,iBAA+B,GAAM;IAC5C,OAAO,mBAAmB;AAC5B;AAEA,SAAS;IACP,IAAI;IACJ,IAAI;IAEJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK;QACnC,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;QACL;QACA,SAAS;QACT,QAAQ;IACV;AACF;AAEA,2CAA2C;AAC3C,+HAA+H;AAE/H,MAAM,kBAAkB,OAAO;AAC/B,MAAM,mBAAmB,OAAO;AAChC,MAAM,iBAAiB,OAAO;AAa9B,SAAS,aAAa,KAAkB;IACtC,IAAI,SAAS,MAAM,MAAM,QAA2B;QAClD,MAAM,MAAM;QACZ,MAAM,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU;QACnC,MAAM,OAAO,CAAC,CAAC,KAAQ,GAAG,UAAU,KAAK,GAAG,UAAU,KAAK;IAC7D;AACF;AAYA,SAAS,SAAS,IAAW;IAC3B,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC3C,IAAI,iBAAiB,MAAM,OAAO;YAClC,IAAI,UAAU,MAAM;gBAClB,MAAM,QAAoB,OAAO,MAAM,CAAC,EAAE,EAAE;oBAC1C,MAAM;gBACR;gBAEA,MAAM,MAAsB;oBAC1B,CAAC,iBAAiB,EAAE,CAAC;oBACrB,CAAC,gBAAgB,EAAE,CAAC,KAAoC,GAAG;gBAC7D;gBAEA,IAAI,IAAI,CACN,CAAC;oBACC,GAAG,CAAC,iBAAiB,GAAG;oBACxB,aAAa;gBACf,GACA,CAAC;oBACC,GAAG,CAAC,eAAe,GAAG;oBACtB,aAAa;gBACf;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;YACL,CAAC,iBAAiB,EAAE;YACpB,CAAC,gBAAgB,EAAE,KAAO;QAC5B;IACF;AACF;AAEA,SAAS,YACP,MAAc,EACd,IAKS,EACT,QAAiB;IAEjB,MAAM,QAAgC,WAClC,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM;IAAsB,KAChD;IAEJ,MAAM,YAA6B,IAAI;IAEvC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;IAEjD,MAAM,UAA8B,OAAO,MAAM,CAAC,YAAY;QAC5D,CAAC,iBAAiB,EAAE,OAAO,OAAO;QAClC,CAAC,gBAAgB,EAAE,CAAC;YAClB,SAAS,GAAG;YACZ,UAAU,OAAO,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,KAAO;QAC1B;IACF;IAEA,MAAM,aAAiC;QACrC;YACE,OAAO;QACT;QACA,KAAI,CAAM;YACR,qCAAqC;YACrC,IAAI,MAAM,SAAS;gBACjB,OAAO,CAAC,iBAAiB,GAAG;YAC9B;QACF;IACF;IAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;IACzC,OAAO,cAAc,CAAC,QAAQ,mBAAmB;IAEjD,SAAS,wBAAwB,IAAW;QAC1C,MAAM,cAAc,SAAS;QAE7B,MAAM,YAAY,IAChB,YAAY,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,eAAe;gBAC9C,OAAO,CAAC,CAAC,iBAAiB;YAC5B;QAEF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,MAAM,KAAmB,OAAO,MAAM,CAAC,IAAM,QAAQ,YAAY;YAC/D,YAAY;QACd;QAEA,SAAS,QAAQ,CAAa;YAC5B,IAAI,MAAM,SAAS,CAAC,UAAU,GAAG,CAAC,IAAI;gBACpC,UAAU,GAAG,CAAC;gBACd,IAAI,KAAK,EAAE,MAAM,QAA6B;oBAC5C,GAAG,UAAU;oBACb,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QAEA,YAAY,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,gBAAgB,CAAC;QAE9C,OAAO,GAAG,UAAU,GAAG,UAAU;IACnC;IAEA,SAAS,YAAY,GAAS;QAC5B,IAAI,KAAK;YACP,OAAQ,OAAO,CAAC,eAAe,GAAG;QACpC,OAAO;YACL,QAAQ,OAAO,CAAC,iBAAiB;QACnC;QAEA,aAAa;IACf;IAEA,KAAK,yBAAyB;IAE9B,IAAI,SAAS,MAAM,MAAM,SAA0B;QACjD,MAAM,MAAM;IACd;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,cAAc,SAAS,YAAuB,QAAgB;IAClE,MAAM,UAAU,IAAI,IAAI,UAAU;IAClC,MAAM,SAA8B,CAAC;IACrC,IAAK,MAAM,OAAO,QAAS,MAAM,CAAC,IAAI,GAAG,AAAC,OAAe,CAAC,IAAI;IAC9D,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG,SAAS,OAAO,CAAC,UAAU;IAC7C,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG;IAClC,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,CAAC,GAAG,QAAsB;IAC5D,IAAK,MAAM,OAAO,OAChB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;QAC/B,YAAY;QACZ,cAAc;QACd,OAAO,MAAM,CAAC,IAAI;IACpB;AACJ;AAEA,YAAY,SAAS,GAAG,IAAI,SAAS;AAErC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD;AAEA;;CAEC,GACD,SAAS,YAAY,SAAmB;IACtC,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0]}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/base-externals-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../shared/runtime-utils.ts\" />\n\n/// A 'base' utilities to support runtime can have externals.\n/// Currently this is for node.js / edge runtime both.\n/// If a fn requires node.js specific behavior, it should be placed in `node-external-utils` instead.\n\nasync function externalImport(id: DependencySpecifier) {\n  let raw\n  try {\n    raw = await import(id)\n  } catch (err) {\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\n    // an external module we don't provide a shim for (e.g. querystring, url).\n    // For now, we fail semi-silently, but in the future this should be a\n    // compilation error.\n    throw new Error(`Failed to load external module ${id}: ${err}`)\n  }\n\n  if (raw && raw.__esModule && raw.default && 'default' in raw.default) {\n    return interopEsm(raw.default, createNS(raw), true)\n  }\n\n  return raw\n}\n\nfunction externalRequire(\n  id: ModuleId,\n  thunk: () => any,\n  esm: boolean = false\n): Exports | EsmNamespaceObject {\n  let raw\n  try {\n    raw = thunk()\n  } catch (err) {\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\n    // an external module we don't provide a shim for (e.g. querystring, url).\n    // For now, we fail semi-silently, but in the future this should be a\n    // compilation error.\n    throw new Error(`Failed to load external module ${id}: ${err}`)\n  }\n\n  if (!esm || raw.__esModule) {\n    return raw\n  }\n\n  return interopEsm(raw, createNS(raw), true)\n}\n\nexternalRequire.resolve = (\n  id: string,\n  options?: {\n    paths?: string[]\n  }\n) => {\n  return require.resolve(id, options)\n}\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AAEnD,6DAA6D;AAC7D,sDAAsD;AACtD,qGAAqG;AAErG,eAAe,eAAe,EAAuB;IACnD,IAAI;IACJ,IAAI;QACF,MAAM,MAAM,MAAM,CAAC;IACrB,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,OAAO,IAAI,UAAU,IAAI,IAAI,OAAO,IAAI,aAAa,IAAI,OAAO,EAAE;QACpE,OAAO,WAAW,IAAI,OAAO,EAAE,SAAS,MAAM;IAChD;IAEA,OAAO;AACT;AAEA,SAAS,gBACP,EAAY,EACZ,KAAgB,EAChB,MAAe,KAAK;IAEpB,IAAI;IACJ,IAAI;QACF,MAAM;IACR,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;QAC1B,OAAO;IACT;IAEA,OAAO,WAAW,KAAK,SAAS,MAAM;AACxC;AAEA,gBAAgB,OAAO,GAAG,CACxB,IACA;IAIA,OAAO,QAAQ,OAAO,CAAC,IAAI;AAC7B", "ignoreList": [0]}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-externals-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\ndeclare var RUNTIME_PUBLIC_PATH: string\ndeclare var RELATIVE_ROOT_PATH: string\ndeclare var ASSET_PREFIX: string\n\nconst path = require('path')\n\nconst relativePathToRuntimeRoot = path.relative(RUNTIME_PUBLIC_PATH, '.')\n// Compute the relative path to the `distDir`.\nconst relativePathToDistRoot = path.join(\n  relativePathToRuntimeRoot,\n  RELATIVE_ROOT_PATH\n)\nconst RUNTIME_ROOT = path.resolve(__filename, relativePathToRuntimeRoot)\n// Compute the absolute path to the root, by stripping distDir from the absolute path to this file.\nconst ABSOLUTE_ROOT = path.resolve(__filename, relativePathToDistRoot)\n\n/**\n * Returns an absolute path to the given module path.\n * Module path should be relative, either path to a file or a directory.\n *\n * This fn allows to calculate an absolute path for some global static values, such as\n * `__dirname` or `import.meta.url` that <PERSON><PERSON> will not embeds in compile time.\n * See ImportMetaBinding::code_generation for the usage.\n */\nfunction resolveAbsolutePath(modulePath?: string): string {\n  if (modulePath) {\n    return path.join(ABSOLUTE_ROOT, modulePath)\n  }\n  return ABSOLUTE_ROOT\n}\n"], "names": [], "mappings": "AAAA,oDAAoD,GAMpD,MAAM,OAAO,QAAQ;AAErB,MAAM,4BAA4B,KAAK,QAAQ,CAAC,qBAAqB;AACrE,8CAA8C;AAC9C,MAAM,yBAAyB,KAAK,IAAI,CACtC,2BACA;AAEF,MAAM,eAAe,KAAK,OAAO,CAAC,YAAY;AAC9C,mGAAmG;AACnG,MAAM,gBAAgB,KAAK,OAAO,CAAC,YAAY;AAE/C;;;;;;;CAOC,GACD,SAAS,oBAAoB,UAAmB;IAC9C,IAAI,YAAY;QACd,OAAO,KAAK,IAAI,CAAC,eAAe;IAClC;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-wasm-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../shared/runtime-utils.ts\" />\n\nfunction readWebAssemblyAsResponse(path: string) {\n  const { createReadStream } = require('fs') as typeof import('fs')\n  const { Readable } = require('stream') as typeof import('stream')\n\n  const stream = createReadStream(path)\n\n  // @ts-ignore unfortunately there's a slight type mismatch with the stream.\n  return new Response(Readable.toWeb(stream), {\n    headers: {\n      'content-type': 'application/wasm',\n    },\n  })\n}\n\nasync function compileWebAssemblyFromPath(\n  path: string\n): Promise<WebAssembly.Module> {\n  const response = readWebAssemblyAsResponse(path)\n\n  return await WebAssembly.compileStreaming(response)\n}\n\nasync function instantiateWebAssemblyFromPath(\n  path: string,\n  importsObj: WebAssembly.Imports\n): Promise<Exports> {\n  const response = readWebAssemblyAsResponse(path)\n\n  const { instance } = await WebAssembly.instantiateStreaming(\n    response,\n    importsObj\n  )\n\n  return instance.exports\n}\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AAEnD,SAAS,0BAA0B,IAAY;IAC7C,MAAM,EAAE,gBAAgB,EAAE,GAAG,QAAQ;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ;IAE7B,MAAM,SAAS,iBAAiB;IAEhC,2EAA2E;IAC3E,OAAO,IAAI,SAAS,SAAS,KAAK,CAAC,SAAS;QAC1C,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAEA,eAAe,2BACb,IAAY;IAEZ,MAAM,WAAW,0BAA0B;IAE3C,OAAO,MAAM,YAAY,gBAAgB,CAAC;AAC5C;AAEA,eAAe,+BACb,IAAY,EACZ,UAA+B;IAE/B,MAAM,WAAW,0BAA0B;IAE3C,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,YAAY,oBAAoB,CACzD,UACA;IAGF,OAAO,SAAS,OAAO;AACzB", "ignoreList": [0]}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/nodejs/runtime.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../shared/runtime-utils.ts\" />\n/// <reference path=\"../shared-node/base-externals-utils.ts\" />\n/// <reference path=\"../shared-node/node-externals-utils.ts\" />\n/// <reference path=\"../shared-node/node-wasm-utils.ts\" />\n\nenum SourceType {\n  /**\n   * The module was instantiated because it was included in an evaluated chunk's\n   * runtime.\n   */\n  Runtime = 0,\n  /**\n   * The module was instantiated because a parent module imported it.\n   */\n  Parent = 1,\n}\n\ntype SourceInfo =\n  | {\n      type: SourceType.Runtime\n      chunkPath: ChunkPath\n    }\n  | {\n      type: SourceType.Parent\n      parentId: ModuleId\n    }\n\nprocess.env.TURBOPACK = '1'\n\nfunction stringifySourceInfo(source: SourceInfo): string {\n  switch (source.type) {\n    case SourceType.Runtime:\n      return `runtime for chunk ${source.chunkPath}`\n    case SourceType.Parent:\n      return `parent module ${source.parentId}`\n    default:\n      invariant(source, (source) => `Unknown source type: ${source?.type}`)\n  }\n}\n\ntype ExternalRequire = (\n  id: ModuleId,\n  thunk: () => any,\n  esm?: boolean\n) => Exports | EsmNamespaceObject\ntype ExternalImport = (id: ModuleId) => Promise<Exports | EsmNamespaceObject>\n\ninterface TurbopackNodeBuildContext extends TurbopackBaseContext<Module> {\n  R: ResolvePathFromModule\n  x: ExternalRequire\n  y: ExternalImport\n}\n\ntype ModuleFactory = (\n  this: Module['exports'],\n  context: TurbopackNodeBuildContext\n) => unknown\n\nconst url = require('url') as typeof import('url')\nconst fs = require('fs/promises') as typeof import('fs/promises')\n\nconst moduleFactories: ModuleFactories = Object.create(null)\nconst moduleCache: ModuleCache<Module> = Object.create(null)\n\n/**\n * Returns an absolute path to the given module's id.\n */\nfunction createResolvePathFromModule(\n  resolver: (moduleId: string) => Exports\n): (moduleId: string) => string {\n  return function resolvePathFromModule(moduleId: string): string {\n    const exported = resolver(moduleId)\n    const exportedPath = exported?.default ?? exported\n    if (typeof exportedPath !== 'string') {\n      return exported as any\n    }\n\n    const strippedAssetPrefix = exportedPath.slice(ASSET_PREFIX.length)\n    const resolved = path.resolve(RUNTIME_ROOT, strippedAssetPrefix)\n\n    return url.pathToFileURL(resolved).href\n  }\n}\n\nfunction loadChunk(chunkData: ChunkData, source?: SourceInfo): void {\n  if (typeof chunkData === 'string') {\n    return loadChunkPath(chunkData, source)\n  } else {\n    return loadChunkPath(chunkData.path, source)\n  }\n}\n\nconst loadedChunks = new Set<ChunkPath>()\n\nfunction loadChunkPath(chunkPath: ChunkPath, source?: SourceInfo): void {\n  if (!isJs(chunkPath)) {\n    // We only support loading JS chunks in Node.js.\n    // This branch can be hit when trying to load a CSS chunk.\n    return\n  }\n\n  if (loadedChunks.has(chunkPath)) {\n    return\n  }\n\n  try {\n    const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n    const chunkModules: CompressedModuleFactories = require(resolved)\n\n    for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\n      if (!moduleFactories[moduleId]) {\n        if (Array.isArray(moduleFactory)) {\n          let [moduleFactoryFn, otherIds] = moduleFactory\n          moduleFactories[moduleId] = moduleFactoryFn\n          for (const otherModuleId of otherIds) {\n            moduleFactories[otherModuleId] = moduleFactoryFn\n          }\n        } else {\n          moduleFactories[moduleId] = moduleFactory\n        }\n      }\n    }\n    loadedChunks.add(chunkPath)\n  } catch (e) {\n    let errorMessage = `Failed to load chunk ${chunkPath}`\n\n    if (source) {\n      errorMessage += ` from ${stringifySourceInfo(source)}`\n    }\n\n    throw new Error(errorMessage, {\n      cause: e,\n    })\n  }\n}\n\nasync function loadChunkAsync(\n  source: SourceInfo,\n  chunkData: ChunkData\n): Promise<any> {\n  const chunkPath = typeof chunkData === 'string' ? chunkData : chunkData.path\n  if (!isJs(chunkPath)) {\n    // We only support loading JS chunks in Node.js.\n    // This branch can be hit when trying to load a CSS chunk.\n    return\n  }\n\n  if (loadedChunks.has(chunkPath)) {\n    return\n  }\n\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n\n  try {\n    const contents = await fs.readFile(resolved, 'utf-8')\n\n    const localRequire = (id: string) => {\n      let resolvedId = require.resolve(id, { paths: [path.dirname(resolved)] })\n      return require(resolvedId)\n    }\n    const module = {\n      exports: {},\n    }\n    // TODO: Use vm.runInThisContext once our minimal supported Node.js version includes https://github.com/nodejs/node/pull/52153\n    // eslint-disable-next-line no-eval -- Can't use vm.runInThisContext due to https://github.com/nodejs/node/issues/52102\n    ;(0, eval)(\n      '(function(module, exports, require, __dirname, __filename) {' +\n        contents +\n        '\\n})' +\n        '\\n//# sourceURL=' +\n        url.pathToFileURL(resolved)\n    )(module, module.exports, localRequire, path.dirname(resolved), resolved)\n\n    const chunkModules: CompressedModuleFactories = module.exports\n    for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\n      if (!moduleFactories[moduleId]) {\n        if (Array.isArray(moduleFactory)) {\n          let [moduleFactoryFn, otherIds] = moduleFactory\n          moduleFactories[moduleId] = moduleFactoryFn\n          for (const otherModuleId of otherIds) {\n            moduleFactories[otherModuleId] = moduleFactoryFn\n          }\n        } else {\n          moduleFactories[moduleId] = moduleFactory\n        }\n      }\n    }\n    loadedChunks.add(chunkPath)\n  } catch (e) {\n    let errorMessage = `Failed to load chunk ${chunkPath}`\n\n    if (source) {\n      errorMessage += ` from ${stringifySourceInfo(source)}`\n    }\n\n    throw new Error(errorMessage, {\n      cause: e,\n    })\n  }\n}\n\nasync function loadChunkAsyncByUrl(source: SourceInfo, chunkUrl: string) {\n  const path = url.fileURLToPath(new URL(chunkUrl, RUNTIME_ROOT)) as ChunkPath\n  return loadChunkAsync(source, path)\n}\n\nfunction loadWebAssembly(\n  chunkPath: ChunkPath,\n  _edgeModule: () => WebAssembly.Module,\n  imports: WebAssembly.Imports\n) {\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n\n  return instantiateWebAssemblyFromPath(resolved, imports)\n}\n\nfunction loadWebAssemblyModule(\n  chunkPath: ChunkPath,\n  _edgeModule: () => WebAssembly.Module\n) {\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n\n  return compileWebAssemblyFromPath(resolved)\n}\n\nfunction getWorkerBlobURL(_chunks: ChunkPath[]): string {\n  throw new Error('Worker blobs are not implemented yet for Node.js')\n}\n\nfunction instantiateModule(id: ModuleId, source: SourceInfo): Module {\n  const moduleFactory = moduleFactories[id]\n  if (typeof moduleFactory !== 'function') {\n    // This can happen if modules incorrectly handle HMR disposes/updates,\n    // e.g. when they keep a `setTimeout` around which still executes old code\n    // and contains e.g. a `require(\"something\")` call.\n    let instantiationReason\n    switch (source.type) {\n      case SourceType.Runtime:\n        instantiationReason = `as a runtime entry of chunk ${source.chunkPath}`\n        break\n      case SourceType.Parent:\n        instantiationReason = `because it was required from module ${source.parentId}`\n        break\n      default:\n        invariant(source, (source) => `Unknown source type: ${source?.type}`)\n    }\n    throw new Error(\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\n    )\n  }\n\n  const module: Module = {\n    exports: {},\n    error: undefined,\n    loaded: false,\n    id,\n    namespaceObject: undefined,\n  }\n  moduleCache[id] = module\n\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\n  try {\n    const r = commonJsRequire.bind(null, module)\n    moduleFactory.call(module.exports, {\n      a: asyncModule.bind(null, module),\n      e: module.exports,\n      r,\n      t: runtimeRequire,\n      x: externalRequire,\n      y: externalImport,\n      f: moduleContext,\n      i: esmImport.bind(null, module),\n      s: esmExport.bind(null, module, module.exports, moduleCache),\n      j: dynamicExport.bind(null, module, module.exports, moduleCache),\n      v: exportValue.bind(null, module, moduleCache),\n      n: exportNamespace.bind(null, module, moduleCache),\n      m: module,\n      c: moduleCache,\n      M: moduleFactories,\n      l: loadChunkAsync.bind(null, { type: SourceType.Parent, parentId: id }),\n      L: loadChunkAsyncByUrl.bind(null, {\n        type: SourceType.Parent,\n        parentId: id,\n      }),\n      w: loadWebAssembly,\n      u: loadWebAssemblyModule,\n      P: resolveAbsolutePath,\n      U: relativeURL,\n      R: createResolvePathFromModule(r),\n      b: getWorkerBlobURL,\n      z: requireStub,\n    })\n  } catch (error) {\n    module.error = error as any\n    throw error\n  }\n\n  module.loaded = true\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\n    interopEsm(module.exports, module.namespaceObject)\n  }\n\n  return module\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it if it is not cached.\n */\n// @ts-ignore\nfunction getOrInstantiateModuleFromParent(\n  id: ModuleId,\n  sourceModule: Module\n): Module {\n  const module = moduleCache[id]\n\n  if (module) {\n    return module\n  }\n\n  return instantiateModule(id, {\n    type: SourceType.Parent,\n    parentId: sourceModule.id,\n  })\n}\n\n/**\n * Instantiates a runtime module.\n */\nfunction instantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): Module {\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath })\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it as a runtime module if it is not cached.\n */\n// @ts-ignore TypeScript doesn't separate this module space from the browser runtime\nfunction getOrInstantiateRuntimeModule(\n  moduleId: ModuleId,\n  chunkPath: ChunkPath\n): Module {\n  const module = moduleCache[moduleId]\n  if (module) {\n    if (module.error) {\n      throw module.error\n    }\n    return module\n  }\n\n  return instantiateRuntimeModule(moduleId, chunkPath)\n}\n\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/\n/**\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\n */\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\n  return regexJsUrl.test(chunkUrlOrPath)\n}\n\nmodule.exports = {\n  getOrInstantiateRuntimeModule,\n  loadChunk,\n}\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AACnD,+DAA+D;AAC/D,+DAA+D;AAC/D,0DAA0D;AAE1D,IAAA,AAAK,oCAAA;IACH;;;GAGC;IAED;;GAEC;WARE;EAAA;AAsBL,QAAQ,GAAG,CAAC,SAAS,GAAG;AAExB,SAAS,oBAAoB,MAAkB;IAC7C,OAAQ,OAAO,IAAI;QACjB;YACE,OAAO,CAAC,kBAAkB,EAAE,OAAO,SAAS,EAAE;QAChD;YACE,OAAO,CAAC,cAAc,EAAE,OAAO,QAAQ,EAAE;QAC3C;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;IACxE;AACF;AAoBA,MAAM,MAAM,QAAQ;AACpB,MAAM,KAAK,QAAQ;AAEnB,MAAM,kBAAmC,OAAO,MAAM,CAAC;AACvD,MAAM,cAAmC,OAAO,MAAM,CAAC;AAEvD;;CAEC,GACD,SAAS,4BACP,QAAuC;IAEvC,OAAO,SAAS,sBAAsB,QAAgB;QACpD,MAAM,WAAW,SAAS;QAC1B,MAAM,eAAe,UAAU,WAAW;QAC1C,IAAI,OAAO,iBAAiB,UAAU;YACpC,OAAO;QACT;QAEA,MAAM,sBAAsB,aAAa,KAAK,CAAC,aAAa,MAAM;QAClE,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;QAE5C,OAAO,IAAI,aAAa,CAAC,UAAU,IAAI;IACzC;AACF;AAEA,SAAS,UAAU,SAAoB,EAAE,MAAmB;IAC1D,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,cAAc,WAAW;IAClC,OAAO;QACL,OAAO,cAAc,UAAU,IAAI,EAAE;IACvC;AACF;AAEA,MAAM,eAAe,IAAI;AAEzB,SAAS,cAAc,SAAoB,EAAE,MAAmB;IAC9D,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D;IACF;IAEA,IAAI,aAAa,GAAG,CAAC,YAAY;QAC/B;IACF;IAEA,IAAI;QACF,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;QAC5C,MAAM,eAA0C,QAAQ;QAExD,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;YACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;gBAC9B,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAChC,IAAI,CAAC,iBAAiB,SAAS,GAAG;oBAClC,eAAe,CAAC,SAAS,GAAG;oBAC5B,KAAK,MAAM,iBAAiB,SAAU;wBACpC,eAAe,CAAC,cAAc,GAAG;oBACnC;gBACF,OAAO;oBACL,eAAe,CAAC,SAAS,GAAG;gBAC9B;YACF;QACF;QACA,aAAa,GAAG,CAAC;IACnB,EAAE,OAAO,GAAG;QACV,IAAI,eAAe,CAAC,qBAAqB,EAAE,WAAW;QAEtD,IAAI,QAAQ;YACV,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,SAAS;QACxD;QAEA,MAAM,IAAI,MAAM,cAAc;YAC5B,OAAO;QACT;IACF;AACF;AAEA,eAAe,eACb,MAAkB,EAClB,SAAoB;IAEpB,MAAM,YAAY,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;IAC5E,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D;IACF;IAEA,IAAI,aAAa,GAAG,CAAC,YAAY;QAC/B;IACF;IAEA,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,IAAI;QACF,MAAM,WAAW,MAAM,GAAG,QAAQ,CAAC,UAAU;QAE7C,MAAM,eAAe,CAAC;YACpB,IAAI,aAAa,QAAQ,OAAO,CAAC,IAAI;gBAAE,OAAO;oBAAC,KAAK,OAAO,CAAC;iBAAU;YAAC;YACvE,OAAO,QAAQ;QACjB;QACA,MAAM,UAAS;YACb,SAAS,CAAC;QACZ;QAGC,CAAC,GAAG,IAAI,EACP,iEACE,WACA,SACA,qBACA,IAAI,aAAa,CAAC,WACpB,SAAQ,QAAO,OAAO,EAAE,cAAc,KAAK,OAAO,CAAC,WAAW;QAEhE,MAAM,eAA0C,QAAO,OAAO;QAC9D,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;YACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;gBAC9B,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAChC,IAAI,CAAC,iBAAiB,SAAS,GAAG;oBAClC,eAAe,CAAC,SAAS,GAAG;oBAC5B,KAAK,MAAM,iBAAiB,SAAU;wBACpC,eAAe,CAAC,cAAc,GAAG;oBACnC;gBACF,OAAO;oBACL,eAAe,CAAC,SAAS,GAAG;gBAC9B;YACF;QACF;QACA,aAAa,GAAG,CAAC;IACnB,EAAE,OAAO,GAAG;QACV,IAAI,eAAe,CAAC,qBAAqB,EAAE,WAAW;QAEtD,IAAI,QAAQ;YACV,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,SAAS;QACxD;QAEA,MAAM,IAAI,MAAM,cAAc;YAC5B,OAAO;QACT;IACF;AACF;AAEA,eAAe,oBAAoB,MAAkB,EAAE,QAAgB;IACrE,MAAM,QAAO,IAAI,aAAa,CAAC,IAAI,IAAI,UAAU;IACjD,OAAO,eAAe,QAAQ;AAChC;AAEA,SAAS,gBACP,SAAoB,EACpB,WAAqC,EACrC,OAA4B;IAE5B,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,+BAA+B,UAAU;AAClD;AAEA,SAAS,sBACP,SAAoB,EACpB,WAAqC;IAErC,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,2BAA2B;AACpC;AAEA,SAAS,iBAAiB,OAAoB;IAC5C,MAAM,IAAI,MAAM;AAClB;AAEA,SAAS,kBAAkB,EAAY,EAAE,MAAkB;IACzD,MAAM,gBAAgB,eAAe,CAAC,GAAG;IACzC,IAAI,OAAO,kBAAkB,YAAY;QACvC,sEAAsE;QACtE,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB;gBACE,sBAAsB,CAAC,4BAA4B,EAAE,OAAO,SAAS,EAAE;gBACvE;YACF;gBACE,sBAAsB,CAAC,oCAAoC,EAAE,OAAO,QAAQ,EAAE;gBAC9E;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,OAAO,EAAE,GAAG,kBAAkB,EAAE,oBAAoB,uFAAuF,CAAC;IAEjJ;IAEA,MAAM,UAAiB;QACrB,SAAS,CAAC;QACV,OAAO;QACP,QAAQ;QACR;QACA,iBAAiB;IACnB;IACA,WAAW,CAAC,GAAG,GAAG;IAElB,4EAA4E;IAC5E,IAAI;QACF,MAAM,IAAI,gBAAgB,IAAI,CAAC,MAAM;QACrC,cAAc,IAAI,CAAC,QAAO,OAAO,EAAE;YACjC,GAAG,YAAY,IAAI,CAAC,MAAM;YAC1B,GAAG,QAAO,OAAO;YACjB;YACA,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,UAAU,IAAI,CAAC,MAAM;YACxB,GAAG,UAAU,IAAI,CAAC,MAAM,SAAQ,QAAO,OAAO,EAAE;YAChD,GAAG,cAAc,IAAI,CAAC,MAAM,SAAQ,QAAO,OAAO,EAAE;YACpD,GAAG,YAAY,IAAI,CAAC,MAAM,SAAQ;YAClC,GAAG,gBAAgB,IAAI,CAAC,MAAM,SAAQ;YACtC,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,eAAe,IAAI,CAAC,MAAM;gBAAE,IAAI;gBAAqB,UAAU;YAAG;YACrE,GAAG,oBAAoB,IAAI,CAAC,MAAM;gBAChC,IAAI;gBACJ,UAAU;YACZ;YACA,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,4BAA4B;YAC/B,GAAG;YACH,GAAG;QACL;IACF,EAAE,OAAO,OAAO;QACd,QAAO,KAAK,GAAG;QACf,MAAM;IACR;IAEA,QAAO,MAAM,GAAG;IAChB,IAAI,QAAO,eAAe,IAAI,QAAO,OAAO,KAAK,QAAO,eAAe,EAAE;QACvE,yDAAyD;QACzD,WAAW,QAAO,OAAO,EAAE,QAAO,eAAe;IACnD;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,aAAa;AACb,SAAS,iCACP,EAAY,EACZ,YAAoB;IAEpB,MAAM,UAAS,WAAW,CAAC,GAAG;IAE9B,IAAI,SAAQ;QACV,OAAO;IACT;IAEA,OAAO,kBAAkB,IAAI;QAC3B,IAAI;QACJ,UAAU,aAAa,EAAE;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,yBACP,QAAkB,EAClB,SAAoB;IAEpB,OAAO,kBAAkB,UAAU;QAAE,IAAI;QAAsB;IAAU;AAC3E;AAEA;;CAEC,GACD,oFAAoF;AACpF,SAAS,8BACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,UAAS,WAAW,CAAC,SAAS;IACpC,IAAI,SAAQ;QACV,IAAI,QAAO,KAAK,EAAE;YAChB,MAAM,QAAO,KAAK;QACpB;QACA,OAAO;IACT;IAEA,OAAO,yBAAyB,UAAU;AAC5C;AAEA,MAAM,aAAa;AACnB;;CAEC,GACD,SAAS,KAAK,cAAoC;IAChD,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0]}}]}