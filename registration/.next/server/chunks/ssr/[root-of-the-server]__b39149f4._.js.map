{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPhoneNumber(value: string): string {\n  // Remove all non-numeric characters\n  const phoneNumber = value.replace(/\\D/g, '')\n  \n  // Format as (XXX) XXX-XXXX for US numbers\n  if (phoneNumber.length >= 10) {\n    return phoneNumber.replace(/(\\d{3})(\\d{3})(\\d{4})/, '($1) $2-$3')\n  }\n  \n  return phoneNumber\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\(\\d{3}\\) \\d{3}-\\d{4}$/\n  return phoneRegex.test(phone)\n}\n\nexport function formatCurrency(amount: number, currency: string = 'KES'): string {\n  return new Intl.NumberFormat('en-KE', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,kBAAkB,KAAa;IAC7C,oCAAoC;IACpC,MAAM,cAAc,MAAM,OAAO,CAAC,OAAO;IAEzC,0CAA0C;IAC1C,IAAI,YAAY,MAAM,IAAI,IAAI;QAC5B,OAAO,YAAY,OAAO,CAAC,yBAAyB;IACtD;IAEA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    loading = false,\n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = [\n      'inline-flex items-center justify-center rounded-lg font-medium',\n      'transition-colors duration-200 focus-visible:outline-none',\n      'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      'disabled:pointer-events-none disabled:opacity-50'\n    ]\n\n    const variants = {\n      primary: [\n        'bg-primary-green text-white hover:bg-primary-green/90',\n        'shadow-sm hover:shadow-md'\n      ],\n      secondary: [\n        'bg-secondary-sienna text-white hover:bg-secondary-sienna/90',\n        'shadow-sm hover:shadow-md'\n      ],\n      outline: [\n        'border border-border bg-background hover:bg-muted',\n        'text-foreground hover:text-foreground'\n      ],\n      ghost: [\n        'hover:bg-muted hover:text-foreground',\n        'text-muted-foreground'\n      ],\n      destructive: [\n        'bg-destructive text-destructive-foreground',\n        'hover:bg-destructive/90 shadow-sm hover:shadow-md'\n      ]\n    }\n\n    const sizes = {\n      sm: 'h-9 px-3 text-sm',\n      md: 'h-11 px-6 text-base',\n      lg: 'h-12 px-8 text-lg'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AASA,MAAM,uBAAS,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;QACjB;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;YACP;YACA;SACD;QACD,WAAW;YACT;YACA;SACD;QACD,SAAS;YACP;YACA;SACD;QACD,OAAO;YACL;YACA;SACD;QACD,aAAa;YACX;YACA;SACD;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6WAAC,qSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAEpB;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRouter } from 'next/navigation'\nimport { But<PERSON> } from '@/components/ui/Button'\n\nexport default function Home() {\n  const router = useRouter()\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-green/5 via-background to-primary-yellow/5\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-4xl mx-auto text-center space-y-8\">\n          {/* Header */}\n          <div className=\"space-y-4\">\n            <div className=\"inline-flex items-center space-x-3 bg-white rounded-full px-6 py-3 shadow-soft\">\n              <div className=\"w-10 h-10 bg-primary-green rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold\">IKIA</span>\n              </div>\n              <span className=\"text-lg font-semibold text-foreground\">2025</span>\n            </div>\n\n            <h1 className=\"text-4xl md:text-6xl font-bold text-foreground leading-tight\">\n              1st International Investment Conference\n            </h1>\n\n            <h2 className=\"text-2xl md:text-3xl font-semibold text-primary-brown\">\n              Indigenous Knowledge Intellectual Assets\n            </h2>\n\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Where ancestral intelligence meets future technology. Join us for a groundbreaking\n              conference exploring investment opportunities in Indigenous Knowledge Intellectual Assets.\n            </p>\n          </div>\n\n          {/* Key Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 my-12\">\n            <div className=\"bg-white rounded-xl p-6 shadow-soft\">\n              <div className=\"text-2xl mb-2\">📅</div>\n              <h3 className=\"font-semibold text-foreground\">Date</h3>\n              <p className=\"text-muted-foreground\">Coming Soon 2025</p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-soft\">\n              <div className=\"text-2xl mb-2\">📍</div>\n              <h3 className=\"font-semibold text-foreground\">Location</h3>\n              <p className=\"text-muted-foreground\">To be announced</p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-soft\">\n              <div className=\"text-2xl mb-2\">🎯</div>\n              <h3 className=\"font-semibold text-foreground\">Focus</h3>\n              <p className=\"text-muted-foreground\">Agri-tech, Clean Energy, Fintech</p>\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"space-y-6\">\n            <div className=\"bg-white rounded-2xl p-8 shadow-medium\">\n              <h3 className=\"text-2xl font-bold text-foreground mb-4\">\n                Ready to Register?\n              </h3>\n              <p className=\"text-muted-foreground mb-6\">\n                Secure your spot at this historic conference. Choose from multiple registration\n                types including attendee, exhibitor, investor, VIP, and sponsor options.\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button\n                  size=\"lg\"\n                  onClick={() => router.push('/registration')}\n                  className=\"text-lg px-8 py-4\"\n                >\n                  Start Registration\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"text-lg px-8 py-4\"\n                >\n                  Learn More\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Features */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-12\">\n            {[\n              { icon: '🌱', title: 'Agri-tech Innovation', desc: 'Sustainable agriculture solutions' },\n              { icon: '⚡', title: 'Clean Energy', desc: 'Renewable energy technologies' },\n              { icon: '💰', title: 'Fintech Solutions', desc: 'Financial technology advances' },\n              { icon: '🏛️', title: 'Cultural Heritage', desc: 'Traditional knowledge preservation' }\n            ].map((feature, index) => (\n              <div key={index} className=\"bg-white/50 rounded-lg p-4 text-center\">\n                <div className=\"text-2xl mb-2\">{feature.icon}</div>\n                <h4 className=\"font-semibold text-sm text-foreground\">{feature.title}</h4>\n                <p className=\"text-xs text-muted-foreground\">{feature.desc}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAEzC,6WAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;0CAG1D,6WAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,6WAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAItE,6WAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAOjE,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6WAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6WAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6WAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,6WAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAKzC,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,6WAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAK1C,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;sDAID,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6WAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,MAAM;gCAAM,OAAO;gCAAwB,MAAM;4BAAoC;4BACvF;gCAAE,MAAM;gCAAK,OAAO;gCAAgB,MAAM;4BAAgC;4BAC1E;gCAAE,MAAM;gCAAM,OAAO;gCAAqB,MAAM;4BAAgC;4BAChF;gCAAE,MAAM;gCAAO,OAAO;gCAAqB,MAAM;4BAAqC;yBACvF,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6WAAC;gCAAgB,WAAU;;kDACzB,6WAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,6WAAC;wCAAG,WAAU;kDAAyC,QAAQ,KAAK;;;;;;kDACpE,6WAAC;wCAAE,WAAU;kDAAiC,QAAQ,IAAI;;;;;;;+BAHlD;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}]}