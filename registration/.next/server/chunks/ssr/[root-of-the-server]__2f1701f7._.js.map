{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/context/RegistrationContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useReducer, ReactNode } from 'react'\nimport { \n  RegistrationState, \n  RegistrationContextType, \n  RegistrationData, \n  RegistrationStep,\n  UserType \n} from '@/types/registration'\n\n// Initial state\nconst initialState: RegistrationState = {\n  currentStep: 'user-type',\n  data: {},\n  errors: {},\n  isSubmitting: false,\n}\n\n// Action types\ntype RegistrationAction =\n  | { type: 'UPDATE_DATA'; payload: Partial<RegistrationData> }\n  | { type: 'NEXT_STEP' }\n  | { type: 'PREV_STEP' }\n  | { type: 'GO_TO_STEP'; payload: RegistrationStep }\n  | { type: 'SET_ERROR'; payload: { field: string; error: string } }\n  | { type: 'CLEAR_ERROR'; payload: string }\n  | { type: 'CLEAR_ALL_ERRORS' }\n  | { type: 'SET_SUBMITTING'; payload: boolean }\n  | { type: 'RESET_REGISTRATION' }\n\n// Step order mapping\nconst getStepOrder = (userType?: UserType): RegistrationStep[] => {\n  return ['user-type', 'details', 'review', 'success']\n}\n\n// Reducer function\nfunction registrationReducer(\n  state: RegistrationState,\n  action: RegistrationAction\n): RegistrationState {\n  switch (action.type) {\n    case 'UPDATE_DATA':\n      return {\n        ...state,\n        data: { ...state.data, ...action.payload },\n      }\n\n    case 'NEXT_STEP': {\n      const stepOrder = getStepOrder(state.data.userType)\n      const currentIndex = stepOrder.indexOf(state.currentStep)\n      const nextIndex = Math.min(currentIndex + 1, stepOrder.length - 1)\n      return {\n        ...state,\n        currentStep: stepOrder[nextIndex],\n      }\n    }\n\n    case 'PREV_STEP': {\n      const stepOrder = getStepOrder(state.data.userType)\n      const currentIndex = stepOrder.indexOf(state.currentStep)\n      const prevIndex = Math.max(currentIndex - 1, 0)\n      return {\n        ...state,\n        currentStep: stepOrder[prevIndex],\n      }\n    }\n\n    case 'GO_TO_STEP':\n      return {\n        ...state,\n        currentStep: action.payload,\n      }\n\n    case 'SET_ERROR':\n      return {\n        ...state,\n        errors: {\n          ...state.errors,\n          [action.payload.field]: action.payload.error,\n        },\n      }\n\n    case 'CLEAR_ERROR': {\n      const { [action.payload]: _, ...remainingErrors } = state.errors\n      return {\n        ...state,\n        errors: remainingErrors,\n      }\n    }\n\n    case 'CLEAR_ALL_ERRORS':\n      return {\n        ...state,\n        errors: {},\n      }\n\n    case 'SET_SUBMITTING':\n      return {\n        ...state,\n        isSubmitting: action.payload,\n      }\n\n    case 'RESET_REGISTRATION':\n      return initialState\n\n    default:\n      return state\n  }\n}\n\n// Create context\nconst RegistrationContext = createContext<RegistrationContextType | undefined>(undefined)\n\n// Provider component\ninterface RegistrationProviderProps {\n  children: ReactNode\n}\n\nexport function RegistrationProvider({ children }: RegistrationProviderProps) {\n  const [state, dispatch] = useReducer(registrationReducer, initialState)\n\n  const updateData = (data: Partial<RegistrationData>) => {\n    dispatch({ type: 'UPDATE_DATA', payload: data })\n  }\n\n  const nextStep = () => {\n    dispatch({ type: 'NEXT_STEP' })\n  }\n\n  const prevStep = () => {\n    dispatch({ type: 'PREV_STEP' })\n  }\n\n  const goToStep = (step: RegistrationStep) => {\n    dispatch({ type: 'GO_TO_STEP', payload: step })\n  }\n\n  const setError = (field: string, error: string) => {\n    dispatch({ type: 'SET_ERROR', payload: { field, error } })\n  }\n\n  const clearError = (field: string) => {\n    dispatch({ type: 'CLEAR_ERROR', payload: field })\n  }\n\n  const clearAllErrors = () => {\n    dispatch({ type: 'CLEAR_ALL_ERRORS' })\n  }\n\n  const submitRegistration = async () => {\n    dispatch({ type: 'SET_SUBMITTING', payload: true })\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // In a real app, you would make an API call here\n      console.log('Submitting registration:', state.data)\n      \n      // Move to success step\n      dispatch({ type: 'GO_TO_STEP', payload: 'success' })\n    } catch (error) {\n      console.error('Registration failed:', error)\n      setError('general', 'Registration failed. Please try again.')\n    } finally {\n      dispatch({ type: 'SET_SUBMITTING', payload: false })\n    }\n  }\n\n  const resetRegistration = () => {\n    dispatch({ type: 'RESET_REGISTRATION' })\n  }\n\n  const contextValue: RegistrationContextType = {\n    state,\n    updateData,\n    nextStep,\n    prevStep,\n    goToStep,\n    setError,\n    clearError,\n    clearAllErrors,\n    submitRegistration,\n    resetRegistration,\n  }\n\n  return (\n    <RegistrationContext.Provider value={contextValue}>\n      {children}\n    </RegistrationContext.Provider>\n  )\n}\n\n// Hook to use the registration context\nexport function useRegistration() {\n  const context = useContext(RegistrationContext)\n  if (context === undefined) {\n    throw new Error('useRegistration must be used within a RegistrationProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWA,gBAAgB;AAChB,MAAM,eAAkC;IACtC,aAAa;IACb,MAAM,CAAC;IACP,QAAQ,CAAC;IACT,cAAc;AAChB;AAcA,qBAAqB;AACrB,MAAM,eAAe,CAAC;IACpB,OAAO;QAAC;QAAa;QAAW;QAAU;KAAU;AACtD;AAEA,mBAAmB;AACnB,SAAS,oBACP,KAAwB,EACxB,MAA0B;IAE1B,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;YAC3C;QAEF,KAAK;YAAa;gBAChB,MAAM,YAAY,aAAa,MAAM,IAAI,CAAC,QAAQ;gBAClD,MAAM,eAAe,UAAU,OAAO,CAAC,MAAM,WAAW;gBACxD,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG,UAAU,MAAM,GAAG;gBAChE,OAAO;oBACL,GAAG,KAAK;oBACR,aAAa,SAAS,CAAC,UAAU;gBACnC;YACF;QAEA,KAAK;YAAa;gBAChB,MAAM,YAAY,aAAa,MAAM,IAAI,CAAC,QAAQ;gBAClD,MAAM,eAAe,UAAU,OAAO,CAAC,MAAM,WAAW;gBACxD,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,aAAa,SAAS,CAAC,UAAU;gBACnC;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa,OAAO,OAAO;YAC7B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBACN,GAAG,MAAM,MAAM;oBACf,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,OAAO,CAAC,KAAK;gBAC9C;YACF;QAEF,KAAK;YAAe;gBAClB,MAAM,EAAE,CAAC,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,GAAG,MAAM,MAAM;gBAChE,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ;gBACV;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,CAAC;YACX;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,cAAc,OAAO,OAAO;YAC9B;QAEF,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAEA,iBAAiB;AACjB,MAAM,oCAAsB,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAAuC;AAOxE,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB;IAE1D,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAK;IAChD;IAEA,MAAM,WAAW;QACf,SAAS;YAAE,MAAM;QAAY;IAC/B;IAEA,MAAM,WAAW;QACf,SAAS;YAAE,MAAM;QAAY;IAC/B;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS;YAAE,MAAM;YAAc,SAAS;QAAK;IAC/C;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,SAAS;YAAE,MAAM;YAAa,SAAS;gBAAE;gBAAO;YAAM;QAAE;IAC1D;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS;YAAE,MAAM;YAAe,SAAS;QAAM;IACjD;IAEA,MAAM,iBAAiB;QACrB,SAAS;YAAE,MAAM;QAAmB;IACtC;IAEA,MAAM,qBAAqB;QACzB,SAAS;YAAE,MAAM;YAAkB,SAAS;QAAK;QAEjD,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iDAAiD;YACjD,QAAQ,GAAG,CAAC,4BAA4B,MAAM,IAAI;YAElD,uBAAuB;YACvB,SAAS;gBAAE,MAAM;gBAAc,SAAS;YAAU;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,WAAW;QACtB,SAAU;YACR,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAM;QACpD;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS;YAAE,MAAM;QAAqB;IACxC;IAEA,MAAM,eAAwC;QAC5C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6WAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPhoneNumber(value: string): string {\n  // Remove all non-numeric characters\n  const phoneNumber = value.replace(/\\D/g, '')\n  \n  // Format as (XXX) XXX-XXXX for US numbers\n  if (phoneNumber.length >= 10) {\n    return phoneNumber.replace(/(\\d{3})(\\d{3})(\\d{4})/, '($1) $2-$3')\n  }\n  \n  return phoneNumber\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  const phoneRegex = /^\\(\\d{3}\\) \\d{3}-\\d{4}$/\n  return phoneRegex.test(phone)\n}\n\nexport function formatCurrency(amount: number, currency: string = 'KES'): string {\n  return new Intl.NumberFormat('en-KE', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,kBAAkB,KAAa;IAC7C,oCAAoC;IACpC,MAAM,cAAc,MAAM,OAAO,CAAC,OAAO;IAEzC,0CAA0C;IAC1C,IAAI,YAAY,MAAM,IAAI,IAAI;QAC5B,OAAO,YAAY,OAAO,CAAC,yBAAyB;IACtD;IAEA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,oBAAoB,KAAa;IAC/C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Check } from 'lucide-react'\n\nexport interface ProgressStep {\n  id: string\n  title: string\n  description?: string\n  status: 'completed' | 'current' | 'upcoming'\n}\n\nexport interface ProgressProps {\n  steps: ProgressStep[]\n  className?: string\n}\n\nconst Progress: React.FC<ProgressProps> = ({ steps, className }) => {\n  return (\n    <nav aria-label=\"Progress\" className={cn('w-full', className)}>\n      <ol className=\"flex items-center justify-between w-full\">\n        {steps.map((step, stepIdx) => (\n          <li key={step.id} className=\"flex-1 relative\">\n            <div className=\"flex items-center\">\n              {/* Step Circle */}\n              <div className=\"flex items-center justify-center relative\">\n                <div\n                  className={cn(\n                    'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors duration-200',\n                    step.status === 'completed' && 'bg-primary-green border-primary-green text-white',\n                    step.status === 'current' && 'bg-primary-green border-primary-green text-white',\n                    step.status === 'upcoming' && 'bg-background border-border text-muted-foreground'\n                  )}\n                >\n                  {step.status === 'completed' ? (\n                    <Check className=\"w-5 h-5\" strokeWidth={2.5} />\n                  ) : (\n                    <span className=\"text-sm font-medium\">{stepIdx + 1}</span>\n                  )}\n                </div>\n              </div>\n\n              {/* Connector Line */}\n              {stepIdx < steps.length - 1 && (\n                <div className=\"flex-1 ml-4\">\n                  <div\n                    className={cn(\n                      'h-0.5 w-full transition-colors duration-200',\n                      step.status === 'completed' ? 'bg-primary-green' : 'bg-border'\n                    )}\n                  />\n                </div>\n              )}\n            </div>\n\n            {/* Step Label */}\n            <div className=\"mt-3 text-center\">\n              <div\n                className={cn(\n                  'text-sm font-medium transition-colors duration-200',\n                  step.status === 'current' && 'text-primary-green',\n                  step.status === 'completed' && 'text-foreground',\n                  step.status === 'upcoming' && 'text-muted-foreground'\n                )}\n              >\n                {step.title}\n              </div>\n              {step.description && (\n                <div className=\"text-xs text-muted-foreground mt-1\">\n                  {step.description}\n                </div>\n              )}\n            </div>\n          </li>\n        ))}\n      </ol>\n    </nav>\n  )\n}\n\nProgress.displayName = 'Progress'\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAcA,MAAM,WAAoC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;IAC7D,qBACE,6WAAC;QAAI,cAAW;QAAW,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;kBACjD,cAAA,6WAAC;YAAG,WAAU;sBACX,MAAM,GAAG,CAAC,CAAC,MAAM,wBAChB,6WAAC;oBAAiB,WAAU;;sCAC1B,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mGACA,KAAK,MAAM,KAAK,eAAe,oDAC/B,KAAK,MAAM,KAAK,aAAa,oDAC7B,KAAK,MAAM,KAAK,cAAc;kDAG/B,KAAK,MAAM,KAAK,4BACf,6WAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,aAAa;;;;;qGAExC,6WAAC;4CAAK,WAAU;sDAAuB,UAAU;;;;;;;;;;;;;;;;gCAMtD,UAAU,MAAM,MAAM,GAAG,mBACxB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+CACA,KAAK,MAAM,KAAK,cAAc,qBAAqB;;;;;;;;;;;;;;;;;sCAQ7D,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,KAAK,MAAM,KAAK,aAAa,sBAC7B,KAAK,MAAM,KAAK,eAAe,mBAC/B,KAAK,MAAM,KAAK,cAAc;8CAG/B,KAAK,KAAK;;;;;;gCAEZ,KAAK,WAAW,kBACf,6WAAC;oCAAI,WAAU;8CACZ,KAAK,WAAW;;;;;;;;;;;;;mBA/ChB,KAAK,EAAE;;;;;;;;;;;;;;;AAwD1B;AAEA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    loading = false,\n    disabled,\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = [\n      'inline-flex items-center justify-center rounded-lg font-medium',\n      'transition-colors duration-200 focus-visible:outline-none',\n      'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      'disabled:pointer-events-none disabled:opacity-50'\n    ]\n\n    const variants = {\n      primary: [\n        'bg-primary-green text-white hover:bg-primary-green/90',\n        'shadow-sm hover:shadow-md'\n      ],\n      secondary: [\n        'bg-secondary-sienna text-white hover:bg-secondary-sienna/90',\n        'shadow-sm hover:shadow-md'\n      ],\n      outline: [\n        'border border-border bg-background hover:bg-muted',\n        'text-foreground hover:text-foreground'\n      ],\n      ghost: [\n        'hover:bg-muted hover:text-foreground',\n        'text-muted-foreground'\n      ],\n      destructive: [\n        'bg-destructive text-destructive-foreground',\n        'hover:bg-destructive/90 shadow-sm hover:shadow-md'\n      ]\n    }\n\n    const sizes = {\n      sm: 'h-9 px-3 text-sm',\n      md: 'h-11 px-6 text-base',\n      lg: 'h-12 px-8 text-lg'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AASA,MAAM,uBAAS,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa;QACjB;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;YACP;YACA;SACD;QACD,WAAW;YACT;YACA;SACD;QACD,SAAS;YACP;YACA;SACD;QACD,OAAO;YACL;YACA;SACD;QACD,aAAa;YACX;YACA;SACD;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6WAAC,qSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAEpB;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/RegistrationLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { Progress } from '@/components/ui/Progress'\nimport { Button } from '@/components/ui/Button'\nimport { ArrowLeft, Home } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface RegistrationLayoutProps {\n  children: React.ReactNode\n  showBackButton?: boolean\n  showProgress?: boolean\n}\n\nexport function RegistrationLayout({ \n  children, \n  showBackButton = true, \n  showProgress = true \n}: RegistrationLayoutProps) {\n  const { state, prevStep } = useRegistration()\n\n  const getProgressSteps = () => {\n    const baseSteps = [\n      {\n        id: 'user-type',\n        title: 'Select Type',\n        description: 'Choose registration type',\n        status: state.currentStep === 'user-type' ? 'current' : \n                state.currentStep === 'details' || state.currentStep === 'review' || state.currentStep === 'success' ? 'completed' : 'upcoming'\n      },\n      {\n        id: 'details',\n        title: 'Your Details',\n        description: 'Personal information',\n        status: state.currentStep === 'details' ? 'current' : \n                state.currentStep === 'review' || state.currentStep === 'success' ? 'completed' : 'upcoming'\n      },\n      {\n        id: 'review',\n        title: 'Review',\n        description: 'Confirm information',\n        status: state.currentStep === 'review' ? 'current' : \n                state.currentStep === 'success' ? 'completed' : 'upcoming'\n      },\n      {\n        id: 'success',\n        title: 'Complete',\n        description: 'Registration complete',\n        status: state.currentStep === 'success' ? 'current' : 'upcoming'\n      }\n    ]\n\n    return baseSteps.map(step => ({\n      ...step,\n      status: step.status as 'completed' | 'current' | 'upcoming'\n    }))\n  }\n\n  const canGoBack = state.currentStep !== 'user-type' && state.currentStep !== 'success'\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-white border-b border-border sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo and Title */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-primary-green rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">IKIA</span>\n                </div>\n                <div>\n                  <h1 className=\"text-lg font-semibold text-foreground\">\n                    Conference Registration\n                  </h1>\n                  <p className=\"text-xs text-muted-foreground\">\n                    IKIA 2025 - Indigenous Knowledge Intellectual Assets\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <div className=\"flex items-center space-x-4\">\n              {showBackButton && canGoBack && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={prevStep}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <ArrowLeft className=\"w-4 h-4\" />\n                  <span>Back</span>\n                </Button>\n              )}\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"flex items-center space-x-2\"\n              >\n                <Home className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">Home</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Progress Indicator */}\n      {showProgress && state.currentStep !== 'success' && (\n        <div className=\"bg-muted/30 border-b border-border\">\n          <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n            <Progress steps={getProgressSteps()} />\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-muted/50 border-t border-border mt-auto\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n            <div className=\"text-sm text-muted-foreground\">\n              © 2025 IKIA Conference. All rights reserved.\n            </div>\n            \n            <div className=\"flex items-center space-x-6 text-sm\">\n              <a \n                href=\"#\" \n                className=\"text-muted-foreground hover:text-primary-green transition-colors\"\n              >\n                Privacy Policy\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-muted-foreground hover:text-primary-green transition-colors\"\n              >\n                Terms & Conditions\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-muted-foreground hover:text-primary-green transition-colors\"\n              >\n                Contact Support\n              </a>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AANA;;;;;;AAeO,SAAS,mBAAmB,EACjC,QAAQ,EACR,iBAAiB,IAAI,EACrB,eAAe,IAAI,EACK;IACxB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAE1C,MAAM,mBAAmB;QACvB,MAAM,YAAY;YAChB;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,QAAQ,MAAM,WAAW,KAAK,cAAc,YACpC,MAAM,WAAW,KAAK,aAAa,MAAM,WAAW,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,cAAc;YAC/H;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,QAAQ,MAAM,WAAW,KAAK,YAAY,YAClC,MAAM,WAAW,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,cAAc;YAC5F;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,QAAQ,MAAM,WAAW,KAAK,WAAW,YACjC,MAAM,WAAW,KAAK,YAAY,cAAc;YAC1D;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,QAAQ,MAAM,WAAW,KAAK,YAAY,YAAY;YACxD;SACD;QAED,OAAO,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM;YACrB,CAAC;IACH;IAEA,MAAM,YAAY,MAAM,WAAW,KAAK,eAAe,MAAM,WAAW,KAAK;IAE7E,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAwC;;;;;;8DAGtD,6WAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;0CAQnD,6WAAC;gCAAI,WAAU;;oCACZ,kBAAkB,2BACjB,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6WAAC;0DAAK;;;;;;;;;;;;kDAIV,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6WAAC,uRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6WAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ5C,gBAAgB,MAAM,WAAW,KAAK,2BACrC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC,oIAAA,CAAA,WAAQ;wBAAC,OAAO;;;;;;;;;;;;;;;;0BAMvB,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,6WAAC;gBAAO,WAAU;0BAChB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CAAgC;;;;;;0CAI/C,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6WAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6WAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode\n  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'\n}\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode\n}\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-border bg-background shadow-soft',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(\n  ({ className, children, as: Component = 'h3', ...props }, ref) => (\n    <Component\n      ref={ref}\n      className={cn(\n        'text-2xl font-semibold leading-none tracking-tight',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </Component>\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    >\n      {children}\n    </p>\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AA2BA,MAAM,qBAAO,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oUAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oUAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,YAAY,IAAI,EAAE,GAAG,OAAO,EAAE,oBACxD,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oUAAA,CAAA,UAAK,CAAC,UAAU,CACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oUAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oUAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/lib/constants.ts"], "sourcesContent": ["import { UserTypeOption, SponsorshipTier, CountryOption, IndustryOption, DietaryRestriction, InterestTrack } from '@/types/registration'\n\nexport const USER_TYPE_OPTIONS: UserTypeOption[] = [\n  {\n    type: 'guest',\n    title: 'Guest/Attendee',\n    description: 'Attending the conference and trade fair sessions',\n    icon: '👤',\n    features: [\n      'Access to all conference sessions',\n      'Networking opportunities',\n      'Exhibition hall access',\n      'Conference materials',\n      'Refreshments and meals'\n    ]\n  },\n  {\n    type: 'exhibitor',\n    title: 'Exhibitor',\n    description: 'Showcasing your products or services at the trade fair',\n    icon: '🏢',\n    features: [\n      'Dedicated exhibition space',\n      'Company branding opportunities',\n      'Lead generation tools',\n      'Networking sessions',\n      'Marketing materials inclusion'\n    ]\n  },\n  {\n    type: 'investor',\n    title: 'Investor',\n    description: 'Looking to explore investment opportunities in IKIAs',\n    icon: '💼',\n    features: [\n      'Exclusive investor sessions',\n      'One-on-one meetings',\n      'Investment pitch access',\n      'Due diligence materials',\n      'VIP networking events'\n    ]\n  },\n  {\n    type: 'vip',\n    title: 'VIP',\n    description: 'Invited guests, dignitaries, or keynote speakers',\n    icon: '⭐',\n    features: [\n      'VIP lounge access',\n      'Priority seating',\n      'Exclusive networking',\n      'Special recognition',\n      'Premium amenities'\n    ]\n  },\n  {\n    type: 'sponsor',\n    title: 'Sponsor',\n    description: 'Supporting the conference through sponsorship',\n    icon: '🤝',\n    features: [\n      'Brand visibility',\n      'Speaking opportunities',\n      'Networking privileges',\n      'Marketing benefits',\n      'Recognition and awards'\n    ]\n  }\n]\n\nexport const SPONSORSHIP_TIERS: SponsorshipTier[] = [\n  {\n    id: 'platinum',\n    name: 'Platinum Sponsor',\n    amount: 5000000,\n    currency: 'KES',\n    highlighted: true,\n    benefits: [\n      'Prominent logo placement on all conference materials',\n      'Keynote speaking slot (15-20 minutes) on main stage',\n      'Dedicated large exhibition space (6x6m) in prime location',\n      '10 VIP delegate passes with full access',\n      'Exclusive branding of specific high-visibility conference area',\n      'Full-page advertisement in official conference program',\n      'Pre- and post-event attendee list (opt-in)',\n      'Dedicated social media campaign and email blast',\n      'Opportunity to host private side event/workshop'\n    ]\n  },\n  {\n    id: 'gold',\n    name: 'Gold Sponsor',\n    amount: 2500000,\n    currency: 'KES',\n    benefits: [\n      'Large logo placement on most conference materials',\n      'Speaking opportunity in panel discussion or breakout session',\n      'Standard exhibition space (3x3m)',\n      '5 VIP delegate passes with full access',\n      'Branding of specific session, workshop, or coffee break',\n      'Half-page advertisement in official conference program',\n      'Social media mentions'\n    ]\n  },\n  {\n    id: 'silver',\n    name: 'Silver Sponsor',\n    amount: 1000000,\n    currency: 'KES',\n    benefits: [\n      'Medium logo placement on select conference materials',\n      'Shared exhibition space (2x2m tabletop)',\n      '3 delegate passes',\n      'Logo inclusion on conference website sponsor page',\n      'Quarter-page advertisement in official conference program'\n    ]\n  },\n  {\n    id: 'bronze',\n    name: 'Bronze Sponsor',\n    amount: 500000,\n    currency: 'KES',\n    benefits: [\n      'Small logo placement on conference website sponsor page',\n      '1 delegate pass',\n      'Mention in official conference program'\n    ]\n  }\n]\n\nexport const COUNTRIES: CountryOption[] = [\n  { code: 'KE', name: 'Kenya', flag: '🇰🇪' },\n  { code: 'UG', name: 'Uganda', flag: '🇺🇬' },\n  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },\n  { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },\n  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },\n  { code: 'ZA', name: 'South Africa', flag: '🇿🇦' },\n  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },\n  { code: 'GH', name: 'Ghana', flag: '🇬🇭' },\n  { code: 'US', name: 'United States', flag: '🇺🇸' },\n  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },\n  { code: 'CA', name: 'Canada', flag: '🇨🇦' },\n  { code: 'AU', name: 'Australia', flag: '🇦🇺' },\n  { code: 'DE', name: 'Germany', flag: '🇩🇪' },\n  { code: 'FR', name: 'France', flag: '🇫🇷' },\n  { code: 'NL', name: 'Netherlands', flag: '🇳🇱' },\n  { code: 'SE', name: 'Sweden', flag: '🇸🇪' },\n  { code: 'NO', name: 'Norway', flag: '🇳🇴' },\n  { code: 'DK', name: 'Denmark', flag: '🇩🇰' },\n  { code: 'CH', name: 'Switzerland', flag: '🇨🇭' },\n  { code: 'JP', name: 'Japan', flag: '🇯🇵' },\n  { code: 'CN', name: 'China', flag: '🇨🇳' },\n  { code: 'IN', name: 'India', flag: '🇮🇳' },\n  { code: 'BR', name: 'Brazil', flag: '🇧🇷' }\n]\n\nexport const INDUSTRIES: IndustryOption[] = [\n  { value: 'agritech', label: 'Agriculture & Technology', category: 'Technology' },\n  { value: 'clean-energy', label: 'Clean Energy & Renewables', category: 'Energy' },\n  { value: 'fintech', label: 'Financial Technology', category: 'Technology' },\n  { value: 'cultural-tourism', label: 'Cultural Tourism', category: 'Tourism' },\n  { value: 'traditional-medicine', label: 'Traditional Medicine', category: 'Healthcare' },\n  { value: 'arts-crafts', label: 'Arts & Crafts', category: 'Creative' },\n  { value: 'sustainable-development', label: 'Sustainable Development', category: 'Development' },\n  { value: 'biotechnology', label: 'Biotechnology', category: 'Technology' },\n  { value: 'education', label: 'Education & Training', category: 'Education' },\n  { value: 'manufacturing', label: 'Manufacturing', category: 'Industry' },\n  { value: 'government', label: 'Government & Public Sector', category: 'Public' },\n  { value: 'ngo', label: 'Non-Governmental Organization', category: 'Non-Profit' },\n  { value: 'consulting', label: 'Consulting Services', category: 'Services' },\n  { value: 'media', label: 'Media & Communications', category: 'Media' },\n  { value: 'other', label: 'Other', category: 'Other' }\n]\n\nexport const DIETARY_RESTRICTIONS: DietaryRestriction[] = [\n  { value: 'vegetarian', label: 'Vegetarian' },\n  { value: 'vegan', label: 'Vegan' },\n  { value: 'gluten-free', label: 'Gluten-Free' },\n  { value: 'halal', label: 'Halal' },\n  { value: 'kosher', label: 'Kosher' },\n  { value: 'dairy-free', label: 'Dairy-Free' },\n  { value: 'nut-free', label: 'Nut-Free' },\n  { value: 'none', label: 'No Restrictions' }\n]\n\nexport const INTEREST_TRACKS: InterestTrack[] = [\n  { \n    value: 'agritech', \n    label: 'Agri-tech Innovation',\n    description: 'Technology solutions for agriculture and food security'\n  },\n  { \n    value: 'clean-energy', \n    label: 'Clean Energy Solutions',\n    description: 'Renewable energy and sustainable power systems'\n  },\n  { \n    value: 'fintech', \n    label: 'Financial Technology',\n    description: 'Digital financial services and inclusion'\n  },\n  { \n    value: 'cultural-tourism', \n    label: 'Cultural Tourism',\n    description: 'Heritage-based tourism and cultural preservation'\n  },\n  { \n    value: 'policy-governance', \n    label: 'Policy & Governance',\n    description: 'Regulatory frameworks and governance structures'\n  },\n  { \n    value: 'ip-protection', \n    label: 'IP Protection',\n    description: 'Intellectual property rights and protection mechanisms'\n  },\n  { \n    value: 'traditional-medicine', \n    label: 'Traditional Medicine',\n    description: 'Indigenous healing practices and modern integration'\n  },\n  { \n    value: 'sustainable-development', \n    label: 'Sustainable Development',\n    description: 'Environmental sustainability and community development'\n  }\n]\n\nexport const INVESTOR_TYPES = [\n  { value: 'angel', label: 'Angel Investor' },\n  { value: 'vc', label: 'Venture Capital' },\n  { value: 'pe', label: 'Private Equity' },\n  { value: 'corporate-vc', label: 'Corporate VC' },\n  { value: 'family-office', label: 'Family Office' },\n  { value: 'fund-manager', label: 'Fund Manager' },\n  { value: 'impact-investor', label: 'Impact Investor' },\n  { value: 'government', label: 'Government Fund' },\n  { value: 'other', label: 'Other' }\n]\n\nexport const INVESTMENT_RANGES = [\n  { value: 'under-100k', label: 'Under $100K' },\n  { value: '100k-500k', label: '$100K - $500K' },\n  { value: '500k-1m', label: '$500K - $1M' },\n  { value: '1m-5m', label: '$1M - $5M' },\n  { value: '5m-10m', label: '$5M - $10M' },\n  { value: 'over-10m', label: 'Over $10M' }\n]\n\nexport const BOOTH_SIZES = [\n  { value: 'standard', label: 'Standard (3x3m)', description: 'Basic booth space with standard amenities' },\n  { value: 'premium', label: 'Premium (6x6m)', description: 'Larger space with enhanced features' },\n  { value: 'custom', label: 'Custom Size', description: 'Tailored booth size based on requirements' }\n]\n\nexport const POWER_REQUIREMENTS = [\n  { value: 'standard', label: 'Standard Outlet (220V)' },\n  { value: 'high-power', label: 'High Power (Industrial)' },\n  { value: 'none', label: 'No Power Required' }\n]\n\nexport const PAYMENT_METHODS = [\n  { value: 'bank-transfer', label: 'Bank Transfer' },\n  { value: 'credit-card', label: 'Credit Card' },\n  { value: 'mobile-money', label: 'Mobile Money (M-Pesa)' },\n  { value: 'cheque', label: 'Cheque' },\n  { value: 'other', label: 'Other (Please specify)' }\n]\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEO,MAAM,oBAAsC;IACjD;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,oBAAuC;IAClD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,UAAU;YACR;YACA;YACA;SACD;IACH;CACD;AAEM,MAAM,YAA6B;IACxC;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;IAAO;IAC1C;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAY,MAAM;IAAO;IAC7C;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAY,MAAM;IAAO;IAC7C;QAAE,MAAM;QAAM,MAAM;QAAgB,MAAM;IAAO;IACjD;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;IAAO;IAC1C;QAAE,MAAM;QAAM,MAAM;QAAiB,MAAM;IAAO;IAClD;QAAE,MAAM;QAAM,MAAM;QAAkB,MAAM;IAAO;IACnD;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAa,MAAM;IAAO;IAC9C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAe,MAAM;IAAO;IAChD;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;IAC3C;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAM,MAAM;QAAe,MAAM;IAAO;IAChD;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;IAAO;IAC1C;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;IAAO;IAC1C;QAAE,MAAM;QAAM,MAAM;QAAS,MAAM;IAAO;IAC1C;QAAE,MAAM;QAAM,MAAM;QAAU,MAAM;IAAO;CAC5C;AAEM,MAAM,aAA+B;IAC1C;QAAE,OAAO;QAAY,OAAO;QAA4B,UAAU;IAAa;IAC/E;QAAE,OAAO;QAAgB,OAAO;QAA6B,UAAU;IAAS;IAChF;QAAE,OAAO;QAAW,OAAO;QAAwB,UAAU;IAAa;IAC1E;QAAE,OAAO;QAAoB,OAAO;QAAoB,UAAU;IAAU;IAC5E;QAAE,OAAO;QAAwB,OAAO;QAAwB,UAAU;IAAa;IACvF;QAAE,OAAO;QAAe,OAAO;QAAiB,UAAU;IAAW;IACrE;QAAE,OAAO;QAA2B,OAAO;QAA2B,UAAU;IAAc;IAC9F;QAAE,OAAO;QAAiB,OAAO;QAAiB,UAAU;IAAa;IACzE;QAAE,OAAO;QAAa,OAAO;QAAwB,UAAU;IAAY;IAC3E;QAAE,OAAO;QAAiB,OAAO;QAAiB,UAAU;IAAW;IACvE;QAAE,OAAO;QAAc,OAAO;QAA8B,UAAU;IAAS;IAC/E;QAAE,OAAO;QAAO,OAAO;QAAiC,UAAU;IAAa;IAC/E;QAAE,OAAO;QAAc,OAAO;QAAuB,UAAU;IAAW;IAC1E;QAAE,OAAO;QAAS,OAAO;QAA0B,UAAU;IAAQ;IACrE;QAAE,OAAO;QAAS,OAAO;QAAS,UAAU;IAAQ;CACrD;AAEM,MAAM,uBAA6C;IACxD;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAQ,OAAO;IAAkB;CAC3C;AAEM,MAAM,kBAAmC;IAC9C;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAS,OAAO;IAAiB;IAC1C;QAAE,OAAO;QAAM,OAAO;IAAkB;IACxC;QAAE,OAAO;QAAM,OAAO;IAAiB;IACvC;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAiB,OAAO;IAAgB;IACjD;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAmB,OAAO;IAAkB;IACrD;QAAE,OAAO;QAAc,OAAO;IAAkB;IAChD;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAEM,MAAM,oBAAoB;IAC/B;QAAE,OAAO;QAAc,OAAO;IAAc;IAC5C;QAAE,OAAO;QAAa,OAAO;IAAgB;IAC7C;QAAE,OAAO;QAAW,OAAO;IAAc;IACzC;QAAE,OAAO;QAAS,OAAO;IAAY;IACrC;QAAE,OAAO;QAAU,OAAO;IAAa;IACvC;QAAE,OAAO;QAAY,OAAO;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB;QAAE,OAAO;QAAY,OAAO;QAAmB,aAAa;IAA4C;IACxG;QAAE,OAAO;QAAW,OAAO;QAAkB,aAAa;IAAsC;IAChG;QAAE,OAAO;QAAU,OAAO;QAAe,aAAa;IAA4C;CACnG;AAEM,MAAM,qBAAqB;IAChC;QAAE,OAAO;QAAY,OAAO;IAAyB;IACrD;QAAE,OAAO;QAAc,OAAO;IAA0B;IACxD;QAAE,OAAO;QAAQ,OAAO;IAAoB;CAC7C;AAEM,MAAM,kBAAkB;IAC7B;QAAE,OAAO;QAAiB,OAAO;IAAgB;IACjD;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAgB,OAAO;IAAwB;IACxD;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAS,OAAO;IAAyB;CACnD", "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/UserTypeSelection.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { USER_TYPE_OPTIONS } from '@/lib/constants'\nimport { UserType } from '@/types/registration'\nimport { cn } from '@/lib/utils'\nimport { Check } from 'lucide-react'\n\nexport function UserTypeSelection() {\n  const { state, updateData, nextStep } = useRegistration()\n  const [selectedType, setSelectedType] = React.useState<UserType | null>(\n    state.data.userType || null\n  )\n\n  const handleTypeSelect = (type: UserType) => {\n    setSelectedType(type)\n    updateData({ userType: type })\n  }\n\n  const handleContinue = () => {\n    if (selectedType) {\n      nextStep()\n    }\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-foreground\">\n          Select Your Registration Type\n        </h1>\n        <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n          Choose the registration type that best describes your participation in the \n          1st International Investment Conference and Trade Fair on Indigenous Knowledge \n          Intellectual Assets (IKIAs) 2025.\n        </p>\n      </div>\n\n      {/* User Type Options */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {USER_TYPE_OPTIONS.map((option) => (\n          <Card\n            key={option.type}\n            className={cn(\n              'cursor-pointer transition-all duration-200 hover:shadow-medium',\n              'border-2 relative overflow-hidden',\n              selectedType === option.type\n                ? 'border-primary-green bg-primary-green/5'\n                : 'border-border hover:border-primary-green/50'\n            )}\n            onClick={() => handleTypeSelect(option.type)}\n          >\n            {/* Selection Indicator */}\n            {selectedType === option.type && (\n              <div className=\"absolute top-4 right-4 w-6 h-6 bg-primary-green rounded-full flex items-center justify-center\">\n                <Check className=\"w-4 h-4 text-white\" strokeWidth={2.5} />\n              </div>\n            )}\n\n            <CardContent className=\"p-6 space-y-4\">\n              {/* Icon and Title */}\n              <div className=\"space-y-3\">\n                <div className=\"text-4xl\">{option.icon}</div>\n                <div>\n                  <h3 className=\"text-xl font-semibold text-foreground\">\n                    {option.title}\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground mt-1\">\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n\n              {/* Features */}\n              <div className=\"space-y-2\">\n                <h4 className=\"text-sm font-medium text-foreground\">\n                  What's included:\n                </h4>\n                <ul className=\"space-y-1\">\n                  {option.features.slice(0, 3).map((feature, index) => (\n                    <li key={index} className=\"text-xs text-muted-foreground flex items-start\">\n                      <span className=\"w-1 h-1 bg-primary-green rounded-full mt-2 mr-2 flex-shrink-0\" />\n                      {feature}\n                    </li>\n                  ))}\n                  {option.features.length > 3 && (\n                    <li className=\"text-xs text-muted-foreground\">\n                      + {option.features.length - 3} more benefits\n                    </li>\n                  )}\n                </ul>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Continue Button */}\n      <div className=\"flex justify-center pt-6\">\n        <Button\n          size=\"lg\"\n          onClick={handleContinue}\n          disabled={!selectedType}\n          className=\"min-w-[200px]\"\n        >\n          Continue to Registration\n        </Button>\n      </div>\n\n      {/* Additional Information */}\n      <div className=\"bg-muted/50 rounded-lg p-6 text-center\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n          Need Help Choosing?\n        </h3>\n        <p className=\"text-sm text-muted-foreground mb-4\">\n          If you're unsure which registration type is right for you, please contact our support team.\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-2 justify-center\">\n          <Button variant=\"outline\" size=\"sm\">\n            Contact Support\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\">\n            View Detailed Comparison\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AATA;;;;;;;;;AAWO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,oUAAA,CAAA,UAAK,CAAC,QAAQ,CACpD,MAAM,IAAI,CAAC,QAAQ,IAAI;IAGzB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,WAAW;YAAE,UAAU;QAAK;IAC9B;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAGnD,6WAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;0BAQjE,6WAAC;gBAAI,WAAU;0BACZ,uHAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,CAAC,uBACtB,6WAAC,gIAAA,CAAA,OAAI;wBAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA,qCACA,iBAAiB,OAAO,IAAI,GACxB,4CACA;wBAEN,SAAS,IAAM,iBAAiB,OAAO,IAAI;;4BAG1C,iBAAiB,OAAO,IAAI,kBAC3B,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;oCAAqB,aAAa;;;;;;;;;;;0CAIvD,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DAAY,OAAO,IAAI;;;;;;0DACtC,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,6WAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;kDAMzB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAAsC;;;;;;0DAGpD,6WAAC;gDAAG,WAAU;;oDACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,6WAAC;4DAAe,WAAU;;8EACxB,6WAAC;oEAAK,WAAU;;;;;;gEACf;;2DAFM;;;;;oDAKV,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,6WAAC;wDAAG,WAAU;;4DAAgC;4DACzC,OAAO,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;uBA7CnC,OAAO,IAAI;;;;;;;;;;0BAwDtB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAS;oBACT,UAAU,CAAC;oBACX,WAAU;8BACX;;;;;;;;;;;0BAMH,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAG3D,6WAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAGlD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CAGpC,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helpText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type = 'text',\n    label,\n    error,\n    helpText,\n    leftIcon,\n    rightIcon,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    \n    const inputStyles = [\n      'flex h-11 w-full rounded-lg border border-border bg-input px-3 py-2',\n      'text-base ring-offset-background file:border-0 file:bg-transparent',\n      'file:text-sm file:font-medium placeholder:text-muted-foreground',\n      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',\n      'focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n      'transition-colors duration-200'\n    ]\n\n    const errorStyles = error ? [\n      'border-destructive focus-visible:ring-destructive'\n    ] : []\n\n    const iconPadding = {\n      left: leftIcon ? 'pl-10' : '',\n      right: rightIcon ? 'pr-10' : ''\n    }\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n          >\n            {label}\n            {props.required && <span className=\"text-destructive ml-1\">*</span>}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {leftIcon}\n            </div>\n          )}\n          \n          <input\n            type={type}\n            className={cn(\n              inputStyles,\n              errorStyles,\n              iconPadding.left,\n              iconPadding.right,\n              className\n            )}\n            ref={ref}\n            id={inputId}\n            {...props}\n          />\n          \n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        \n        {error && (\n          <p className=\"text-sm text-destructive font-medium\">\n            {error}\n          </p>\n        )}\n        \n        {helpText && !error && (\n          <p className=\"text-sm text-muted-foreground\">\n            {helpText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,sBAAQ,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EACC,SAAS,EACT,OAAO,MAAM,EACb,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc,QAAQ;QAC1B;KACD,GAAG,EAAE;IAEN,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU;QAC3B,OAAO,YAAY,UAAU;IAC/B;IAEA,qBACE,6WAAC;QAAI,WAAU;;YACZ,uBACC,6WAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6WAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI/D,6WAAC;gBAAI,WAAU;;oBACZ,0BACC,6WAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6WAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,aACA,YAAY,IAAI,EAChB,YAAY,KAAK,EACjB;wBAEF,KAAK;wBACL,IAAI;wBACH,GAAG,KAAK;;;;;;oBAGV,2BACC,6WAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAKN,uBACC,6WAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,YAAY,CAAC,uBACZ,6WAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Select.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { ChevronDown } from 'lucide-react'\n\nexport interface SelectOption {\n  value: string\n  label: string\n  disabled?: boolean\n}\n\nexport interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'children'> {\n  label?: string\n  error?: string\n  helpText?: string\n  options: SelectOption[]\n  placeholder?: string\n}\n\nconst Select = React.forwardRef<HTMLSelectElement, SelectProps>(\n  ({ \n    className, \n    label,\n    error,\n    helpText,\n    options,\n    placeholder = 'Select an option...',\n    id,\n    ...props \n  }, ref) => {\n    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`\n    \n    const selectStyles = [\n      'flex h-11 w-full rounded-lg border border-border bg-input px-3 py-2',\n      'text-base ring-offset-background focus-visible:outline-none',\n      'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      'disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200',\n      'appearance-none cursor-pointer'\n    ]\n\n    const errorStyles = error ? [\n      'border-destructive focus-visible:ring-destructive'\n    ] : []\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label \n            htmlFor={selectId}\n            className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n          >\n            {label}\n            {props.required && <span className=\"text-destructive ml-1\">*</span>}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          <select\n            className={cn(\n              selectStyles,\n              errorStyles,\n              className\n            )}\n            ref={ref}\n            id={selectId}\n            {...props}\n          >\n            {placeholder && (\n              <option value=\"\" disabled>\n                {placeholder}\n              </option>\n            )}\n            {options.map((option) => (\n              <option \n                key={option.value} \n                value={option.value}\n                disabled={option.disabled}\n              >\n                {option.label}\n              </option>\n            ))}\n          </select>\n          \n          <ChevronDown className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none\" />\n        </div>\n        \n        {error && (\n          <p className=\"text-sm text-destructive font-medium\">\n            {error}\n          </p>\n        )}\n        \n        {helpText && !error && (\n          <p className=\"text-sm text-muted-foreground\">\n            {helpText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nSelect.displayName = 'Select'\n\nexport { Select }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAgBA,MAAM,uBAAS,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EACC,SAAS,EACT,KAAK,EACL,KAAK,EACL,QAAQ,EACR,OAAO,EACP,cAAc,qBAAqB,EACnC,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc,QAAQ;QAC1B;KACD,GAAG,EAAE;IAEN,qBACE,6WAAC;QAAI,WAAU;;YACZ,uBACC,6WAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6WAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI/D,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cACA,aACA;wBAEF,KAAK;wBACL,IAAI;wBACH,GAAG,KAAK;;4BAER,6BACC,6WAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAGJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6WAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCASvB,6WAAC,wSAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;YAGxB,uBACC,6WAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,YAAY,CAAC,uBACZ,6WAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Textarea.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  label?: string\n  error?: string\n  helpText?: string\n  maxLength?: number\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ \n    className, \n    label,\n    error,\n    helpText,\n    maxLength,\n    id,\n    value,\n    ...props \n  }, ref) => {\n    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`\n    const currentLength = typeof value === 'string' ? value.length : 0\n    \n    const textareaStyles = [\n      'flex min-h-[80px] w-full rounded-lg border border-border bg-input px-3 py-2',\n      'text-base ring-offset-background placeholder:text-muted-foreground',\n      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',\n      'focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n      'transition-colors duration-200 resize-vertical'\n    ]\n\n    const errorStyles = error ? [\n      'border-destructive focus-visible:ring-destructive'\n    ] : []\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label \n            htmlFor={textareaId}\n            className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n          >\n            {label}\n            {props.required && <span className=\"text-destructive ml-1\">*</span>}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          <textarea\n            className={cn(\n              textareaStyles,\n              errorStyles,\n              className\n            )}\n            ref={ref}\n            id={textareaId}\n            value={value}\n            maxLength={maxLength}\n            {...props}\n          />\n          \n          {maxLength && (\n            <div className=\"absolute bottom-2 right-2 text-xs text-muted-foreground bg-background px-1 rounded\">\n              {currentLength}/{maxLength}\n            </div>\n          )}\n        </div>\n        \n        {error && (\n          <p className=\"text-sm text-destructive font-medium\">\n            {error}\n          </p>\n        )}\n        \n        {helpText && !error && (\n          <p className=\"text-sm text-muted-foreground\">\n            {helpText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nTextarea.displayName = 'Textarea'\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,yBAAW,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC/B,CAAC,EACC,SAAS,EACT,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC9E,MAAM,gBAAgB,OAAO,UAAU,WAAW,MAAM,MAAM,GAAG;IAEjE,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc,QAAQ;QAC1B;KACD,GAAG,EAAE;IAEN,qBACE,6WAAC;QAAI,WAAU;;YACZ,uBACC,6WAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6WAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAI/D,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,aACA;wBAEF,KAAK;wBACL,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACV,GAAG,KAAK;;;;;;oBAGV,2BACC,6WAAC;wBAAI,WAAU;;4BACZ;4BAAc;4BAAE;;;;;;;;;;;;;YAKtB,uBACC,6WAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,YAAY,CAAC,uBACZ,6WAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/ui/Checkbox.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Check } from 'lucide-react'\n\nexport interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {\n  label?: string\n  description?: string\n  error?: string\n}\n\nconst Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(\n  ({ \n    className, \n    label,\n    description,\n    error,\n    id,\n    checked,\n    ...props \n  }, ref) => {\n    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`\n    \n    return (\n      <div className=\"space-y-2\">\n        <div className=\"flex items-start space-x-3\">\n          <div className=\"relative flex items-center\">\n            <input\n              type=\"checkbox\"\n              className=\"sr-only\"\n              ref={ref}\n              id={checkboxId}\n              checked={checked}\n              {...props}\n            />\n            <div\n              className={cn(\n                'flex h-5 w-5 items-center justify-center rounded border-2 transition-colors duration-200',\n                'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',\n                checked \n                  ? 'bg-primary-green border-primary-green text-white' \n                  : 'border-border bg-input hover:border-primary-green/50',\n                error && 'border-destructive',\n                props.disabled && 'opacity-50 cursor-not-allowed',\n                className\n              )}\n            >\n              {checked && (\n                <Check className=\"h-3 w-3 text-white\" strokeWidth={3} />\n              )}\n            </div>\n          </div>\n          \n          {(label || description) && (\n            <div className=\"flex-1 space-y-1\">\n              {label && (\n                <label \n                  htmlFor={checkboxId}\n                  className={cn(\n                    'text-sm font-medium leading-none cursor-pointer',\n                    props.disabled && 'cursor-not-allowed opacity-70'\n                  )}\n                >\n                  {label}\n                  {props.required && <span className=\"text-destructive ml-1\">*</span>}\n                </label>\n              )}\n              \n              {description && (\n                <p className=\"text-sm text-muted-foreground\">\n                  {description}\n                </p>\n              )}\n            </div>\n          )}\n        </div>\n        \n        {error && (\n          <p className=\"text-sm text-destructive font-medium ml-8\">\n            {error}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\n\nCheckbox.displayName = 'Checkbox'\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQA,MAAM,yBAAW,oUAAA,CAAA,UAAK,CAAC,UAAU,CAC/B,CAAC,EACC,SAAS,EACT,KAAK,EACL,WAAW,EACX,KAAK,EACL,EAAE,EACF,OAAO,EACP,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE9E,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCACC,MAAK;gCACL,WAAU;gCACV,KAAK;gCACL,IAAI;gCACJ,SAAS;gCACR,GAAG,KAAK;;;;;;0CAEX,6WAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,yEACA,UACI,qDACA,wDACJ,SAAS,sBACT,MAAM,QAAQ,IAAI,iCAClB;0CAGD,yBACC,6WAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;oCAAqB,aAAa;;;;;;;;;;;;;;;;;oBAKxD,CAAC,SAAS,WAAW,mBACpB,6WAAC;wBAAI,WAAU;;4BACZ,uBACC,6WAAC;gCACC,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mDACA,MAAM,QAAQ,IAAI;;oCAGnB;oCACA,MAAM,QAAQ,kBAAI,6WAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;4BAI9D,6BACC,6WAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;YAOV,uBACC,6WAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAGF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/GuestRegistrationForm.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Input } from '@/components/ui/Input'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Checkbox } from '@/components/ui/Checkbox'\nimport { Button } from '@/components/ui/Button'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { COUNTRIES, DIETARY_RESTRICTIONS, INTEREST_TRACKS } from '@/lib/constants'\nimport { GuestRegistrationData } from '@/types/registration'\nimport { User, Mail, Phone, Globe, Building, Utensils, Accessibility, Target } from 'lucide-react'\n\nconst guestSchema = z.object({\n  fullName: z.string().min(2, 'Full name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),\n  country: z.string().min(1, 'Please select your country'),\n  organization: z.string().optional(),\n  dietaryRestrictions: z.array(z.string()).default([]),\n  accessibilityNeeds: z.string().optional(),\n  interestTracks: z.array(z.string()).min(1, 'Please select at least one interest track'),\n})\n\ntype GuestFormData = z.infer<typeof guestSchema>\n\nexport function GuestRegistrationForm() {\n  const { state, updateData, nextStep } = useRegistration()\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    setValue,\n    watch,\n    trigger\n  } = useForm<GuestFormData>({\n    resolver: zodResolver(guestSchema),\n    defaultValues: {\n      fullName: state.data.fullName || '',\n      email: state.data.email || '',\n      phoneNumber: state.data.phoneNumber || '',\n      country: state.data.country || '',\n      organization: (state.data as GuestRegistrationData)?.organization || '',\n      dietaryRestrictions: (state.data as GuestRegistrationData)?.dietaryRestrictions || [],\n      accessibilityNeeds: (state.data as GuestRegistrationData)?.accessibilityNeeds || '',\n      interestTracks: (state.data as GuestRegistrationData)?.interestTracks || [],\n    }\n  })\n\n  const watchedDietaryRestrictions = watch('dietaryRestrictions')\n  const watchedInterestTracks = watch('interestTracks')\n\n  const handleDietaryRestrictionChange = (value: string, checked: boolean) => {\n    const current = watchedDietaryRestrictions || []\n    if (checked) {\n      setValue('dietaryRestrictions', [...current, value])\n    } else {\n      setValue('dietaryRestrictions', current.filter(item => item !== value))\n    }\n    trigger('dietaryRestrictions')\n  }\n\n  const handleInterestTrackChange = (value: string, checked: boolean) => {\n    const current = watchedInterestTracks || []\n    if (checked) {\n      setValue('interestTracks', [...current, value])\n    } else {\n      setValue('interestTracks', current.filter(item => item !== value))\n    }\n    trigger('interestTracks')\n  }\n\n  const onSubmit = (data: GuestFormData) => {\n    const registrationData: Partial<GuestRegistrationData> = {\n      userType: 'guest',\n      ...data,\n    }\n    updateData(registrationData)\n    nextStep()\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-foreground\">\n          Guest/Attendee Registration\n        </h1>\n        <p className=\"text-lg text-muted-foreground\">\n          Complete your registration to attend the IKIA 2025 conference\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-8\">\n        {/* Personal Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <User className=\"w-5 h-5 text-primary-green\" />\n              <span>Personal Information</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Full Name\"\n                placeholder=\"Enter your full name\"\n                leftIcon={<User className=\"w-4 h-4\" />}\n                error={errors.fullName?.message}\n                required\n                {...register('fullName')}\n              />\n              \n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"<EMAIL>\"\n                leftIcon={<Mail className=\"w-4 h-4\" />}\n                error={errors.email?.message}\n                required\n                {...register('email')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Phone Number\"\n                type=\"tel\"\n                placeholder=\"+****************\"\n                leftIcon={<Phone className=\"w-4 h-4\" />}\n                error={errors.phoneNumber?.message}\n                required\n                {...register('phoneNumber')}\n              />\n              \n              <Select\n                label=\"Country of Residence\"\n                placeholder=\"Select your country\"\n                options={COUNTRIES.map(country => ({\n                  value: country.code,\n                  label: `${country.flag} ${country.name}`\n                }))}\n                error={errors.country?.message}\n                required\n                {...register('country')}\n              />\n            </div>\n\n            <Input\n              label=\"Organization/Affiliation\"\n              placeholder=\"Your company, university, or organization (optional)\"\n              leftIcon={<Building className=\"w-4 h-4\" />}\n              helpText=\"This field is optional but helps us understand our attendees better\"\n              {...register('organization')}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Dietary Restrictions */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Utensils className=\"w-5 h-5 text-primary-green\" />\n              <span>Dietary Restrictions</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {DIETARY_RESTRICTIONS.map((restriction) => (\n                <Checkbox\n                  key={restriction.value}\n                  label={restriction.label}\n                  checked={watchedDietaryRestrictions?.includes(restriction.value) || false}\n                  onChange={(e) => handleDietaryRestrictionChange(restriction.value, e.target.checked)}\n                />\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Accessibility Needs */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Accessibility className=\"w-5 h-5 text-primary-green\" />\n              <span>Accessibility Needs</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <Textarea\n              label=\"Special Accessibility Requirements\"\n              placeholder=\"Please describe any accessibility accommodations you may need (wheelchair access, sign language interpretation, etc.)\"\n              rows={4}\n              maxLength={500}\n              helpText=\"We are committed to making our event accessible to all attendees\"\n              {...register('accessibilityNeeds')}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Interest Tracks */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Target className=\"w-5 h-5 text-primary-green\" />\n              <span>Conference Interest Tracks</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <p className=\"text-sm text-muted-foreground\">\n              Select the conference tracks that interest you most. This helps us personalize your experience.\n            </p>\n            \n            <div className=\"space-y-3\">\n              {INTEREST_TRACKS.map((track) => (\n                <Checkbox\n                  key={track.value}\n                  label={track.label}\n                  description={track.description}\n                  checked={watchedInterestTracks?.includes(track.value) || false}\n                  onChange={(e) => handleInterestTrackChange(track.value, e.target.checked)}\n                />\n              ))}\n            </div>\n            \n            {errors.interestTracks && (\n              <p className=\"text-sm text-destructive font-medium\">\n                {errors.interestTracks.message}\n              </p>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <Button\n            type=\"submit\"\n            size=\"lg\"\n            loading={isSubmitting}\n            className=\"min-w-[200px]\"\n          >\n            Continue to Review\n          </Button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;;;;;AAiBA,MAAM,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAChC,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,qBAAqB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACnD,oBAAoB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,gBAAgB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;AAC7C;AAIO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEtD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,QAAQ,EACR,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU,MAAM,IAAI,CAAC,QAAQ,IAAI;YACjC,OAAO,MAAM,IAAI,CAAC,KAAK,IAAI;YAC3B,aAAa,MAAM,IAAI,CAAC,WAAW,IAAI;YACvC,SAAS,MAAM,IAAI,CAAC,OAAO,IAAI;YAC/B,cAAc,AAAC,MAAM,IAAI,EAA4B,gBAAgB;YACrE,qBAAqB,AAAC,MAAM,IAAI,EAA4B,uBAAuB,EAAE;YACrF,oBAAoB,AAAC,MAAM,IAAI,EAA4B,sBAAsB;YACjF,gBAAgB,AAAC,MAAM,IAAI,EAA4B,kBAAkB,EAAE;QAC7E;IACF;IAEA,MAAM,6BAA6B,MAAM;IACzC,MAAM,wBAAwB,MAAM;IAEpC,MAAM,iCAAiC,CAAC,OAAe;QACrD,MAAM,UAAU,8BAA8B,EAAE;QAChD,IAAI,SAAS;YACX,SAAS,uBAAuB;mBAAI;gBAAS;aAAM;QACrD,OAAO;YACL,SAAS,uBAAuB,QAAQ,MAAM,CAAC,CAAA,OAAQ,SAAS;QAClE;QACA,QAAQ;IACV;IAEA,MAAM,4BAA4B,CAAC,OAAe;QAChD,MAAM,UAAU,yBAAyB,EAAE;QAC3C,IAAI,SAAS;YACX,SAAS,kBAAkB;mBAAI;gBAAS;aAAM;QAChD,OAAO;YACL,SAAS,kBAAkB,QAAQ,MAAM,CAAC,CAAA,OAAQ,SAAS;QAC7D;QACA,QAAQ;IACV;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,mBAAmD;YACvD,UAAU;YACV,GAAG,IAAI;QACT;QACA,WAAW;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAGnD,6WAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,6WAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,QAAQ,EAAE;gDACxB,QAAQ;gDACP,GAAG,SAAS,WAAW;;;;;;0DAG1B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,KAAK,EAAE;gDACrB,QAAQ;gDACP,GAAG,SAAS,QAAQ;;;;;;;;;;;;kDAIzB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAC3B,OAAO,OAAO,WAAW,EAAE;gDAC3B,QAAQ;gDACP,GAAG,SAAS,cAAc;;;;;;0DAG7B,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAM;gDACN,aAAY;gDACZ,SAAS,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;wDACjC,OAAO,QAAQ,IAAI;wDACnB,OAAO,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;oDAC1C,CAAC;gDACD,OAAO,OAAO,OAAO,EAAE;gDACvB,QAAQ;gDACP,GAAG,SAAS,UAAU;;;;;;;;;;;;kDAI3B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,aAAY;wCACZ,wBAAU,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAC9B,UAAS;wCACR,GAAG,SAAS,eAAe;;;;;;;;;;;;;;;;;;kCAMlC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,6WAAC;oCAAI,WAAU;8CACZ,uHAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,CAAC,4BACzB,6WAAC,oIAAA,CAAA,WAAQ;4CAEP,OAAO,YAAY,KAAK;4CACxB,SAAS,4BAA4B,SAAS,YAAY,KAAK,KAAK;4CACpE,UAAU,CAAC,IAAM,+BAA+B,YAAY,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;2CAH9E,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;kCAWhC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,wSAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,6WAAC,oIAAA,CAAA,WAAQ;oCACP,OAAM;oCACN,aAAY;oCACZ,MAAM;oCACN,WAAW;oCACX,UAAS;oCACR,GAAG,SAAS,qBAAqB;;;;;;;;;;;;;;;;;kCAMxC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,6WAAC;wCAAI,WAAU;kDACZ,uHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,sBACpB,6WAAC,oIAAA,CAAA,WAAQ;gDAEP,OAAO,MAAM,KAAK;gDAClB,aAAa,MAAM,WAAW;gDAC9B,SAAS,uBAAuB,SAAS,MAAM,KAAK,KAAK;gDACzD,UAAU,CAAC,IAAM,0BAA0B,MAAM,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;+CAJnE,MAAM,KAAK;;;;;;;;;;oCASrB,OAAO,cAAc,kBACpB,6WAAC;wCAAE,WAAU;kDACV,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;kCAOtC,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/ExhibitorRegistrationForm.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Input } from '@/components/ui/Input'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Checkbox } from '@/components/ui/Checkbox'\nimport { Button } from '@/components/ui/Button'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { COUNTRIES, INDUSTRIES, BOOTH_SIZES, POWER_REQUIREMENTS } from '@/lib/constants'\nimport { ExhibitorRegistrationData } from '@/types/registration'\nimport { User, Mail, Phone, Globe, Building, FileText, Upload, Zap, Users } from 'lucide-react'\n\nconst exhibitorSchema = z.object({\n  fullName: z.string().min(2, 'Full name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),\n  country: z.string().min(1, 'Please select your country'),\n  jobTitle: z.string().min(2, 'Job title is required'),\n  companyName: z.string().min(2, 'Company name is required'),\n  companyWebsite: z.string().url('Please enter a valid URL').optional().or(z.literal('')),\n  companyDescription: z.string().min(50, 'Company description must be at least 50 characters').max(1000, 'Company description must be less than 1000 characters'),\n  primaryIndustry: z.string().min(1, 'Please select your primary industry'),\n  productsServices: z.string().min(50, 'Please describe your products/services (minimum 50 characters)'),\n  boothSize: z.enum(['standard', 'premium', 'custom'], { required_error: 'Please select a booth size' }),\n  powerRequirements: z.array(z.string()).default([]),\n  staffCount: z.number().min(1, 'At least 1 staff member is required').max(20, 'Maximum 20 staff members allowed'),\n})\n\ntype ExhibitorFormData = z.infer<typeof exhibitorSchema>\n\nexport function ExhibitorRegistrationForm() {\n  const { state, updateData, nextStep } = useRegistration()\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    setValue,\n    watch,\n    trigger\n  } = useForm<ExhibitorFormData>({\n    resolver: zodResolver(exhibitorSchema),\n    defaultValues: {\n      fullName: state.data.fullName || '',\n      email: state.data.email || '',\n      phoneNumber: state.data.phoneNumber || '',\n      country: state.data.country || '',\n      jobTitle: (state.data as ExhibitorRegistrationData)?.jobTitle || '',\n      companyName: (state.data as ExhibitorRegistrationData)?.companyName || '',\n      companyWebsite: (state.data as ExhibitorRegistrationData)?.companyWebsite || '',\n      companyDescription: (state.data as ExhibitorRegistrationData)?.companyDescription || '',\n      primaryIndustry: (state.data as ExhibitorRegistrationData)?.primaryIndustry || '',\n      productsServices: (state.data as ExhibitorRegistrationData)?.productsServices || '',\n      boothSize: (state.data as ExhibitorRegistrationData)?.boothSize || 'standard',\n      powerRequirements: (state.data as ExhibitorRegistrationData)?.powerRequirements || [],\n      staffCount: (state.data as ExhibitorRegistrationData)?.staffCount || 1,\n    }\n  })\n\n  const watchedPowerRequirements = watch('powerRequirements')\n\n  const handlePowerRequirementChange = (value: string, checked: boolean) => {\n    const current = watchedPowerRequirements || []\n    if (checked) {\n      setValue('powerRequirements', [...current, value])\n    } else {\n      setValue('powerRequirements', current.filter(item => item !== value))\n    }\n    trigger('powerRequirements')\n  }\n\n  const onSubmit = (data: ExhibitorFormData) => {\n    const registrationData: Partial<ExhibitorRegistrationData> = {\n      userType: 'exhibitor',\n      ...data,\n    }\n    updateData(registrationData)\n    nextStep()\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-foreground\">\n          Exhibitor Registration\n        </h1>\n        <p className=\"text-lg text-muted-foreground\">\n          Register to showcase your products and services at IKIA 2025\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-8\">\n        {/* Contact Person Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <User className=\"w-5 h-5 text-primary-green\" />\n              <span>Contact Person Details</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Full Name\"\n                placeholder=\"Enter your full name\"\n                leftIcon={<User className=\"w-4 h-4\" />}\n                error={errors.fullName?.message}\n                required\n                {...register('fullName')}\n              />\n              \n              <Input\n                label=\"Job Title\"\n                placeholder=\"Your position in the company\"\n                error={errors.jobTitle?.message}\n                required\n                {...register('jobTitle')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"<EMAIL>\"\n                leftIcon={<Mail className=\"w-4 h-4\" />}\n                error={errors.email?.message}\n                required\n                {...register('email')}\n              />\n              \n              <Input\n                label=\"Phone Number\"\n                type=\"tel\"\n                placeholder=\"+****************\"\n                leftIcon={<Phone className=\"w-4 h-4\" />}\n                error={errors.phoneNumber?.message}\n                required\n                {...register('phoneNumber')}\n              />\n            </div>\n\n            <Select\n              label=\"Country\"\n              placeholder=\"Select your country\"\n              options={COUNTRIES.map(country => ({\n                value: country.code,\n                label: `${country.flag} ${country.name}`\n              }))}\n              error={errors.country?.message}\n              required\n              {...register('country')}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Company Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Building className=\"w-5 h-5 text-primary-green\" />\n              <span>Company Information</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Company Name\"\n                placeholder=\"Your company name\"\n                leftIcon={<Building className=\"w-4 h-4\" />}\n                error={errors.companyName?.message}\n                required\n                {...register('companyName')}\n              />\n              \n              <Input\n                label=\"Company Website\"\n                type=\"url\"\n                placeholder=\"https://www.yourcompany.com\"\n                leftIcon={<Globe className=\"w-4 h-4\" />}\n                error={errors.companyWebsite?.message}\n                helpText=\"Optional but recommended\"\n                {...register('companyWebsite')}\n              />\n            </div>\n\n            <Textarea\n              label=\"Company Description\"\n              placeholder=\"Describe your company, its mission, and what makes it unique...\"\n              rows={4}\n              maxLength={1000}\n              error={errors.companyDescription?.message}\n              helpText=\"This will be used in promotional materials (50-1000 characters)\"\n              required\n              {...register('companyDescription')}\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Select\n                label=\"Primary Industry\"\n                placeholder=\"Select your industry\"\n                options={INDUSTRIES.map(industry => ({\n                  value: industry.value,\n                  label: industry.label\n                }))}\n                error={errors.primaryIndustry?.message}\n                required\n                {...register('primaryIndustry')}\n              />\n            </div>\n\n            <Textarea\n              label=\"Products/Services to Exhibit\"\n              placeholder=\"Describe the specific products or services you plan to showcase at the exhibition...\"\n              rows={4}\n              error={errors.productsServices?.message}\n              helpText=\"Be specific about what visitors can expect to see at your booth\"\n              required\n              {...register('productsServices')}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Booth Requirements */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <FileText className=\"w-5 h-5 text-primary-green\" />\n              <span>Booth Requirements</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div>\n              <label className=\"text-sm font-medium leading-none mb-4 block\">\n                Preferred Booth Size <span className=\"text-destructive\">*</span>\n              </label>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {BOOTH_SIZES.map((size) => (\n                  <div key={size.value} className=\"space-y-2\">\n                    <label className=\"flex items-center space-x-3 cursor-pointer\">\n                      <input\n                        type=\"radio\"\n                        value={size.value}\n                        {...register('boothSize')}\n                        className=\"sr-only\"\n                      />\n                      <div className=\"w-4 h-4 border-2 border-border rounded-full flex items-center justify-center\">\n                        <div className=\"w-2 h-2 bg-primary-green rounded-full opacity-0 peer-checked:opacity-100\" />\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-sm\">{size.label}</div>\n                        <div className=\"text-xs text-muted-foreground\">{size.description}</div>\n                      </div>\n                    </label>\n                  </div>\n                ))}\n              </div>\n              {errors.boothSize && (\n                <p className=\"text-sm text-destructive font-medium mt-2\">\n                  {errors.boothSize.message}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium leading-none mb-4 block flex items-center space-x-2\">\n                <Zap className=\"w-4 h-4\" />\n                <span>Power Requirements</span>\n              </label>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {POWER_REQUIREMENTS.map((requirement) => (\n                  <Checkbox\n                    key={requirement.value}\n                    label={requirement.label}\n                    checked={watchedPowerRequirements?.includes(requirement.value) || false}\n                    onChange={(e) => handlePowerRequirementChange(requirement.value, e.target.checked)}\n                  />\n                ))}\n              </div>\n            </div>\n\n            <Input\n              label=\"Number of Staff Attending\"\n              type=\"number\"\n              min=\"1\"\n              max=\"20\"\n              placeholder=\"1\"\n              leftIcon={<Users className=\"w-4 h-4\" />}\n              error={errors.staffCount?.message}\n              helpText=\"How many staff members will be working at your booth?\"\n              required\n              {...register('staffCount', { valueAsNumber: true })}\n            />\n          </CardContent>\n        </Card>\n\n        {/* File Uploads */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Upload className=\"w-5 h-5 text-primary-green\" />\n              <span>Company Materials</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium leading-none\">\n                  Company Logo\n                </label>\n                <div className=\"border-2 border-dashed border-border rounded-lg p-6 text-center\">\n                  <Upload className=\"w-8 h-8 text-muted-foreground mx-auto mb-2\" />\n                  <p className=\"text-sm text-muted-foreground\">\n                    Upload your company logo\n                  </p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    PNG, JPG up to 5MB\n                  </p>\n                  <input\n                    type=\"file\"\n                    accept=\"image/*\"\n                    className=\"hidden\"\n                    id=\"company-logo\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"mt-2\"\n                    onClick={() => document.getElementById('company-logo')?.click()}\n                  >\n                    Choose File\n                  </Button>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium leading-none\">\n                  Company Profile/Brochure\n                </label>\n                <div className=\"border-2 border-dashed border-border rounded-lg p-6 text-center\">\n                  <FileText className=\"w-8 h-8 text-muted-foreground mx-auto mb-2\" />\n                  <p className=\"text-sm text-muted-foreground\">\n                    Upload company brochure\n                  </p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    PDF up to 10MB\n                  </p>\n                  <input\n                    type=\"file\"\n                    accept=\".pdf\"\n                    className=\"hidden\"\n                    id=\"company-brochure\"\n                  />\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"mt-2\"\n                    onClick={() => document.getElementById('company-brochure')?.click()}\n                  >\n                    Choose File\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <Button\n            type=\"submit\"\n            size=\"lg\"\n            loading={isSubmitting}\n            className=\"min-w-[200px]\"\n          >\n            Continue to Review\n          </Button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;;;;;AAiBA,MAAM,kBAAkB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAChC,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,4BAA4B,QAAQ,GAAG,EAAE,CAAC,6NAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACnF,oBAAoB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,sDAAsD,GAAG,CAAC,MAAM;IACvG,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,kBAAkB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IACrC,WAAW,6NAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAW;KAAS,EAAE;QAAE,gBAAgB;IAA6B;IACpG,mBAAmB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACjD,YAAY,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,IAAI;AAC/E;AAIO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEtD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,QAAQ,EACR,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAqB;QAC7B,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU,MAAM,IAAI,CAAC,QAAQ,IAAI;YACjC,OAAO,MAAM,IAAI,CAAC,KAAK,IAAI;YAC3B,aAAa,MAAM,IAAI,CAAC,WAAW,IAAI;YACvC,SAAS,MAAM,IAAI,CAAC,OAAO,IAAI;YAC/B,UAAU,AAAC,MAAM,IAAI,EAAgC,YAAY;YACjE,aAAa,AAAC,MAAM,IAAI,EAAgC,eAAe;YACvE,gBAAgB,AAAC,MAAM,IAAI,EAAgC,kBAAkB;YAC7E,oBAAoB,AAAC,MAAM,IAAI,EAAgC,sBAAsB;YACrF,iBAAiB,AAAC,MAAM,IAAI,EAAgC,mBAAmB;YAC/E,kBAAkB,AAAC,MAAM,IAAI,EAAgC,oBAAoB;YACjF,WAAW,AAAC,MAAM,IAAI,EAAgC,aAAa;YACnE,mBAAmB,AAAC,MAAM,IAAI,EAAgC,qBAAqB,EAAE;YACrF,YAAY,AAAC,MAAM,IAAI,EAAgC,cAAc;QACvE;IACF;IAEA,MAAM,2BAA2B,MAAM;IAEvC,MAAM,+BAA+B,CAAC,OAAe;QACnD,MAAM,UAAU,4BAA4B,EAAE;QAC9C,IAAI,SAAS;YACX,SAAS,qBAAqB;mBAAI;gBAAS;aAAM;QACnD,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,CAAC,CAAA,OAAQ,SAAS;QAChE;QACA,QAAQ;IACV;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,mBAAuD;YAC3D,UAAU;YACV,GAAG,IAAI;QACT;QACA,WAAW;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAGnD,6WAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,6WAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,QAAQ,EAAE;gDACxB,QAAQ;gDACP,GAAG,SAAS,WAAW;;;;;;0DAG1B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,OAAO,OAAO,QAAQ,EAAE;gDACxB,QAAQ;gDACP,GAAG,SAAS,WAAW;;;;;;;;;;;;kDAI5B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,KAAK,EAAE;gDACrB,QAAQ;gDACP,GAAG,SAAS,QAAQ;;;;;;0DAGvB,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAC3B,OAAO,OAAO,WAAW,EAAE;gDAC3B,QAAQ;gDACP,GAAG,SAAS,cAAc;;;;;;;;;;;;kDAI/B,6WAAC,kIAAA,CAAA,SAAM;wCACL,OAAM;wCACN,aAAY;wCACZ,SAAS,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;gDACjC,OAAO,QAAQ,IAAI;gDACnB,OAAO,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;4CAC1C,CAAC;wCACD,OAAO,OAAO,OAAO,EAAE;wCACvB,QAAQ;wCACP,GAAG,SAAS,UAAU;;;;;;;;;;;;;;;;;;kCAM7B,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,wBAAU,6WAAC,8RAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAC9B,OAAO,OAAO,WAAW,EAAE;gDAC3B,QAAQ;gDACP,GAAG,SAAS,cAAc;;;;;;0DAG7B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAC3B,OAAO,OAAO,cAAc,EAAE;gDAC9B,UAAS;gDACR,GAAG,SAAS,iBAAiB;;;;;;;;;;;;kDAIlC,6WAAC,oIAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,MAAM;wCACN,WAAW;wCACX,OAAO,OAAO,kBAAkB,EAAE;wCAClC,UAAS;wCACT,QAAQ;wCACP,GAAG,SAAS,qBAAqB;;;;;;kDAGpC,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;4CACL,OAAM;4CACN,aAAY;4CACZ,SAAS,uHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAA,WAAY,CAAC;oDACnC,OAAO,SAAS,KAAK;oDACrB,OAAO,SAAS,KAAK;gDACvB,CAAC;4CACD,OAAO,OAAO,eAAe,EAAE;4CAC/B,QAAQ;4CACP,GAAG,SAAS,kBAAkB;;;;;;;;;;;kDAInC,6WAAC,oIAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,MAAM;wCACN,OAAO,OAAO,gBAAgB,EAAE;wCAChC,UAAS;wCACT,QAAQ;wCACP,GAAG,SAAS,mBAAmB;;;;;;;;;;;;;;;;;;kCAMtC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,kSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;;oDAA8C;kEACxC,6WAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAE1D,6WAAC;gDAAI,WAAU;0DACZ,uHAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,qBAChB,6WAAC;wDAAqB,WAAU;kEAC9B,cAAA,6WAAC;4DAAM,WAAU;;8EACf,6WAAC;oEACC,MAAK;oEACL,OAAO,KAAK,KAAK;oEAChB,GAAG,SAAS,YAAY;oEACzB,WAAU;;;;;;8EAEZ,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,6WAAC;;sFACC,6WAAC;4EAAI,WAAU;sFAAuB,KAAK,KAAK;;;;;;sFAChD,6WAAC;4EAAI,WAAU;sFAAiC,KAAK,WAAW;;;;;;;;;;;;;;;;;;uDAb5D,KAAK,KAAK;;;;;;;;;;4CAmBvB,OAAO,SAAS,kBACf,6WAAC;gDAAE,WAAU;0DACV,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kDAK/B,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;;kEACf,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6WAAC;kEAAK;;;;;;;;;;;;0DAER,6WAAC;gDAAI,WAAU;0DACZ,uHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,4BACvB,6WAAC,oIAAA,CAAA,WAAQ;wDAEP,OAAO,YAAY,KAAK;wDACxB,SAAS,0BAA0B,SAAS,YAAY,KAAK,KAAK;wDAClE,UAAU,CAAC,IAAM,6BAA6B,YAAY,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;uDAH5E,YAAY,KAAK;;;;;;;;;;;;;;;;kDAS9B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,aAAY;wCACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAC3B,OAAO,OAAO,UAAU,EAAE;wCAC1B,UAAS;wCACT,QAAQ;wCACP,GAAG,SAAS,cAAc;4CAAE,eAAe;wCAAK,EAAE;;;;;;;;;;;;;;;;;;kCAMzD,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAM,WAAU;8DAAmC;;;;;;8DAGpD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6WAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAGlD,6WAAC;4DACC,MAAK;4DACL,QAAO;4DACP,WAAU;4DACV,IAAG;;;;;;sEAEL,6WAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,SAAS,cAAc,CAAC,iBAAiB;sEACzD;;;;;;;;;;;;;;;;;;sDAML,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAM,WAAU;8DAAmC;;;;;;8DAGpD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kSAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6WAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6WAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAGlD,6WAAC;4DACC,MAAK;4DACL,QAAO;4DACP,WAAU;4DACV,IAAG;;;;;;sEAEL,6WAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,SAAS,cAAc,CAAC,qBAAqB;sEAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3532, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/InvestorRegistrationForm.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Input } from '@/components/ui/Input'\nimport { Select } from '@/components/ui/Select'\nimport { Textarea } from '@/components/ui/Textarea'\nimport { Checkbox } from '@/components/ui/Checkbox'\nimport { Button } from '@/components/ui/Button'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { COUNTRIES, INVESTOR_TYPES, INVESTMENT_RANGES, INTEREST_TRACKS } from '@/lib/constants'\nimport { InvestorRegistrationData } from '@/types/registration'\nimport { User, Mail, Phone, Globe, Building, DollarSign, Target, Calendar, Upload } from 'lucide-react'\n\nconst investorSchema = z.object({\n  fullName: z.string().min(2, 'Full name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),\n  country: z.string().min(1, 'Please select your country'),\n  companyName: z.string().min(2, 'Company/Fund name is required'),\n  companyWebsite: z.string().url('Please enter a valid URL').optional().or(z.literal('')),\n  investorType: z.string().min(1, 'Please select your investor type'),\n  investmentSectors: z.array(z.string()).min(1, 'Please select at least one investment sector'),\n  investmentRange: z.string().min(1, 'Please select your typical investment range'),\n  geographicFocus: z.array(z.string()).min(1, 'Please select at least one geographic focus'),\n  ikiaInterests: z.string().optional(),\n  availableForMeetings: z.boolean().default(false),\n})\n\ntype InvestorFormData = z.infer<typeof investorSchema>\n\nconst GEOGRAPHIC_FOCUS_OPTIONS = [\n  { value: 'local', label: 'Local (Same Country)' },\n  { value: 'regional', label: 'Regional (East Africa)' },\n  { value: 'continental', label: 'Continental (Africa)' },\n  { value: 'international', label: 'International (Global)' },\n]\n\nexport function InvestorRegistrationForm() {\n  const { state, updateData, nextStep } = useRegistration()\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    setValue,\n    watch,\n    trigger\n  } = useForm<InvestorFormData>({\n    resolver: zodResolver(investorSchema),\n    defaultValues: {\n      fullName: state.data.fullName || '',\n      email: state.data.email || '',\n      phoneNumber: state.data.phoneNumber || '',\n      country: state.data.country || '',\n      companyName: (state.data as InvestorRegistrationData)?.companyName || '',\n      companyWebsite: (state.data as InvestorRegistrationData)?.companyWebsite || '',\n      investorType: (state.data as InvestorRegistrationData)?.investorType || '',\n      investmentSectors: (state.data as InvestorRegistrationData)?.investmentSectors || [],\n      investmentRange: (state.data as InvestorRegistrationData)?.investmentRange || '',\n      geographicFocus: (state.data as InvestorRegistrationData)?.geographicFocus || [],\n      ikiaInterests: (state.data as InvestorRegistrationData)?.ikiaInterests || '',\n      availableForMeetings: (state.data as InvestorRegistrationData)?.availableForMeetings || false,\n    }\n  })\n\n  const watchedInvestmentSectors = watch('investmentSectors')\n  const watchedGeographicFocus = watch('geographicFocus')\n\n  const handleInvestmentSectorChange = (value: string, checked: boolean) => {\n    const current = watchedInvestmentSectors || []\n    if (checked) {\n      setValue('investmentSectors', [...current, value])\n    } else {\n      setValue('investmentSectors', current.filter(item => item !== value))\n    }\n    trigger('investmentSectors')\n  }\n\n  const handleGeographicFocusChange = (value: string, checked: boolean) => {\n    const current = watchedGeographicFocus || []\n    if (checked) {\n      setValue('geographicFocus', [...current, value])\n    } else {\n      setValue('geographicFocus', current.filter(item => item !== value))\n    }\n    trigger('geographicFocus')\n  }\n\n  const onSubmit = (data: InvestorFormData) => {\n    const registrationData: Partial<InvestorRegistrationData> = {\n      userType: 'investor',\n      ...data,\n    }\n    updateData(registrationData)\n    nextStep()\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-foreground\">\n          Investor Registration\n        </h1>\n        <p className=\"text-lg text-muted-foreground\">\n          Register to explore investment opportunities in Indigenous Knowledge Intellectual Assets\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-8\">\n        {/* Personal/Company Details */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <User className=\"w-5 h-5 text-primary-green\" />\n              <span>Personal & Company Details</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Full Name\"\n                placeholder=\"Enter your full name\"\n                leftIcon={<User className=\"w-4 h-4\" />}\n                error={errors.fullName?.message}\n                required\n                {...register('fullName')}\n              />\n              \n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"<EMAIL>\"\n                leftIcon={<Mail className=\"w-4 h-4\" />}\n                error={errors.email?.message}\n                required\n                {...register('email')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Phone Number\"\n                type=\"tel\"\n                placeholder=\"+****************\"\n                leftIcon={<Phone className=\"w-4 h-4\" />}\n                error={errors.phoneNumber?.message}\n                required\n                {...register('phoneNumber')}\n              />\n              \n              <Select\n                label=\"Country\"\n                placeholder=\"Select your country\"\n                options={COUNTRIES.map(country => ({\n                  value: country.code,\n                  label: `${country.flag} ${country.name}`\n                }))}\n                error={errors.country?.message}\n                required\n                {...register('country')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Input\n                label=\"Company/Fund Name\"\n                placeholder=\"Your investment company or fund\"\n                leftIcon={<Building className=\"w-4 h-4\" />}\n                error={errors.companyName?.message}\n                required\n                {...register('companyName')}\n              />\n              \n              <Input\n                label=\"Company Website\"\n                type=\"url\"\n                placeholder=\"https://www.yourcompany.com\"\n                leftIcon={<Globe className=\"w-4 h-4\" />}\n                error={errors.companyWebsite?.message}\n                helpText=\"Optional but recommended\"\n                {...register('companyWebsite')}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Investment Profile */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <DollarSign className=\"w-5 h-5 text-primary-green\" />\n              <span>Investment Profile</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Select\n                label=\"Investor Type\"\n                placeholder=\"Select your investor type\"\n                options={INVESTOR_TYPES}\n                error={errors.investorType?.message}\n                required\n                {...register('investorType')}\n              />\n              \n              <Select\n                label=\"Typical Investment Range\"\n                placeholder=\"Select investment range\"\n                options={INVESTMENT_RANGES}\n                error={errors.investmentRange?.message}\n                required\n                {...register('investmentRange')}\n              />\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium leading-none mb-4 block\">\n                Primary Investment Sectors <span className=\"text-destructive\">*</span>\n              </label>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                {INTEREST_TRACKS.map((track) => (\n                  <Checkbox\n                    key={track.value}\n                    label={track.label}\n                    description={track.description}\n                    checked={watchedInvestmentSectors?.includes(track.value) || false}\n                    onChange={(e) => handleInvestmentSectorChange(track.value, e.target.checked)}\n                  />\n                ))}\n              </div>\n              {errors.investmentSectors && (\n                <p className=\"text-sm text-destructive font-medium mt-2\">\n                  {errors.investmentSectors.message}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium leading-none mb-4 block\">\n                Geographic Investment Focus <span className=\"text-destructive\">*</span>\n              </label>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                {GEOGRAPHIC_FOCUS_OPTIONS.map((option) => (\n                  <Checkbox\n                    key={option.value}\n                    label={option.label}\n                    checked={watchedGeographicFocus?.includes(option.value) || false}\n                    onChange={(e) => handleGeographicFocusChange(option.value, e.target.checked)}\n                  />\n                ))}\n              </div>\n              {errors.geographicFocus && (\n                <p className=\"text-sm text-destructive font-medium mt-2\">\n                  {errors.geographicFocus.message}\n                </p>\n              )}\n            </div>\n\n            <Textarea\n              label=\"Specific IKIA Types of Interest\"\n              placeholder=\"Describe specific types of Indigenous Knowledge Intellectual Assets you're interested in investing in...\"\n              rows={4}\n              helpText=\"Optional: Help us understand your specific interests within IKIAs\"\n              {...register('ikiaInterests')}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Matchmaking & Meetings */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Calendar className=\"w-5 h-5 text-primary-green\" />\n              <span>Matchmaking & Meetings</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-6\">\n            <Checkbox\n              label=\"Available for 1-on-1 Meetings\"\n              description=\"I am interested in scheduling private meetings with entrepreneurs and innovators during the conference\"\n              {...register('availableForMeetings')}\n            />\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium leading-none\">\n                Investor Profile/Portfolio\n              </label>\n              <div className=\"border-2 border-dashed border-border rounded-lg p-6 text-center\">\n                <Upload className=\"w-8 h-8 text-muted-foreground mx-auto mb-2\" />\n                <p className=\"text-sm text-muted-foreground\">\n                  Upload your investor profile or portfolio\n                </p>\n                <p className=\"text-xs text-muted-foreground mt-1\">\n                  PDF up to 10MB (Optional)\n                </p>\n                <input\n                  type=\"file\"\n                  accept=\".pdf\"\n                  className=\"hidden\"\n                  id=\"investor-profile\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  className=\"mt-2\"\n                  onClick={() => document.getElementById('investor-profile')?.click()}\n                >\n                  Choose File\n                </Button>\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                This helps entrepreneurs understand your investment criteria and portfolio\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <Button\n            type=\"submit\"\n            size=\"lg\"\n            loading={isSubmitting}\n            className=\"min-w-[200px]\"\n          >\n            Continue to Review\n          </Button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;;;;;AAiBA,MAAM,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,UAAU,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAChC,SAAS,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,aAAa,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,gBAAgB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,4BAA4B,QAAQ,GAAG,EAAE,CAAC,6NAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACnF,cAAc,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,mBAAmB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;IAC9C,iBAAiB,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,iBAAiB,6NAAA,CAAA,IAAC,CAAC,KAAK,CAAC,6NAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;IAC5C,eAAe,6NAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,sBAAsB,6NAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAC5C;AAIA,MAAM,2BAA2B;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAuB;IAChD;QAAE,OAAO;QAAY,OAAO;IAAyB;IACrD;QAAE,OAAO;QAAe,OAAO;IAAuB;IACtD;QAAE,OAAO;QAAiB,OAAO;IAAyB;CAC3D;AAEM,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEtD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,QAAQ,EACR,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU,MAAM,IAAI,CAAC,QAAQ,IAAI;YACjC,OAAO,MAAM,IAAI,CAAC,KAAK,IAAI;YAC3B,aAAa,MAAM,IAAI,CAAC,WAAW,IAAI;YACvC,SAAS,MAAM,IAAI,CAAC,OAAO,IAAI;YAC/B,aAAa,AAAC,MAAM,IAAI,EAA+B,eAAe;YACtE,gBAAgB,AAAC,MAAM,IAAI,EAA+B,kBAAkB;YAC5E,cAAc,AAAC,MAAM,IAAI,EAA+B,gBAAgB;YACxE,mBAAmB,AAAC,MAAM,IAAI,EAA+B,qBAAqB,EAAE;YACpF,iBAAiB,AAAC,MAAM,IAAI,EAA+B,mBAAmB;YAC9E,iBAAiB,AAAC,MAAM,IAAI,EAA+B,mBAAmB,EAAE;YAChF,eAAe,AAAC,MAAM,IAAI,EAA+B,iBAAiB;YAC1E,sBAAsB,AAAC,MAAM,IAAI,EAA+B,wBAAwB;QAC1F;IACF;IAEA,MAAM,2BAA2B,MAAM;IACvC,MAAM,yBAAyB,MAAM;IAErC,MAAM,+BAA+B,CAAC,OAAe;QACnD,MAAM,UAAU,4BAA4B,EAAE;QAC9C,IAAI,SAAS;YACX,SAAS,qBAAqB;mBAAI;gBAAS;aAAM;QACnD,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,CAAC,CAAA,OAAQ,SAAS;QAChE;QACA,QAAQ;IACV;IAEA,MAAM,8BAA8B,CAAC,OAAe;QAClD,MAAM,UAAU,0BAA0B,EAAE;QAC5C,IAAI,SAAS;YACX,SAAS,mBAAmB;mBAAI;gBAAS;aAAM;QACjD,OAAO;YACL,SAAS,mBAAmB,QAAQ,MAAM,CAAC,CAAA,OAAQ,SAAS;QAC9D;QACA,QAAQ;IACV;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,mBAAsD;YAC1D,UAAU;YACV,GAAG,IAAI;QACT;QACA,WAAW;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAGnD,6WAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,6WAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,QAAQ,EAAE;gDACxB,QAAQ;gDACP,GAAG,SAAS,WAAW;;;;;;0DAG1B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAC1B,OAAO,OAAO,KAAK,EAAE;gDACrB,QAAQ;gDACP,GAAG,SAAS,QAAQ;;;;;;;;;;;;kDAIzB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAC3B,OAAO,OAAO,WAAW,EAAE;gDAC3B,QAAQ;gDACP,GAAG,SAAS,cAAc;;;;;;0DAG7B,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAM;gDACN,aAAY;gDACZ,SAAS,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;wDACjC,OAAO,QAAQ,IAAI;wDACnB,OAAO,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;oDAC1C,CAAC;gDACD,OAAO,OAAO,OAAO,EAAE;gDACvB,QAAQ;gDACP,GAAG,SAAS,UAAU;;;;;;;;;;;;kDAI3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,wBAAU,6WAAC,8RAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAC9B,OAAO,OAAO,WAAW,EAAE;gDAC3B,QAAQ;gDACP,GAAG,SAAS,cAAc;;;;;;0DAG7B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,wBAAU,6WAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAC3B,OAAO,OAAO,cAAc,EAAE;gDAC9B,UAAS;gDACR,GAAG,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAM;gDACN,aAAY;gDACZ,SAAS,uHAAA,CAAA,iBAAc;gDACvB,OAAO,OAAO,YAAY,EAAE;gDAC5B,QAAQ;gDACP,GAAG,SAAS,eAAe;;;;;;0DAG9B,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAM;gDACN,aAAY;gDACZ,SAAS,uHAAA,CAAA,oBAAiB;gDAC1B,OAAO,OAAO,eAAe,EAAE;gDAC/B,QAAQ;gDACP,GAAG,SAAS,kBAAkB;;;;;;;;;;;;kDAInC,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;;oDAA8C;kEAClC,6WAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAEhE,6WAAC;gDAAI,WAAU;0DACZ,uHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,sBACpB,6WAAC,oIAAA,CAAA,WAAQ;wDAEP,OAAO,MAAM,KAAK;wDAClB,aAAa,MAAM,WAAW;wDAC9B,SAAS,0BAA0B,SAAS,MAAM,KAAK,KAAK;wDAC5D,UAAU,CAAC,IAAM,6BAA6B,MAAM,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;uDAJtE,MAAM,KAAK;;;;;;;;;;4CAQrB,OAAO,iBAAiB,kBACvB,6WAAC;gDAAE,WAAU;0DACV,OAAO,iBAAiB,CAAC,OAAO;;;;;;;;;;;;kDAKvC,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;;oDAA8C;kEACjC,6WAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;0DAEjE,6WAAC;gDAAI,WAAU;0DACZ,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,6WAAC,oIAAA,CAAA,WAAQ;wDAEP,OAAO,OAAO,KAAK;wDACnB,SAAS,wBAAwB,SAAS,OAAO,KAAK,KAAK;wDAC3D,UAAU,CAAC,IAAM,4BAA4B,OAAO,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;uDAHtE,OAAO,KAAK;;;;;;;;;;4CAOtB,OAAO,eAAe,kBACrB,6WAAC;gDAAE,WAAU;0DACV,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;kDAKrC,6WAAC,oIAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,MAAM;wCACN,UAAS;wCACR,GAAG,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;kCAMnC,6WAAC,gIAAA,CAAA,OAAI;;0CACH,6WAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6WAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6WAAC,oIAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACX,GAAG,SAAS,uBAAuB;;;;;;kDAGtC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAM,WAAU;0DAAmC;;;;;;0DAGpD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6WAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAG7C,6WAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAGlD,6WAAC;wDACC,MAAK;wDACL,QAAO;wDACP,WAAU;wDACV,IAAG;;;;;;kEAEL,6WAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,SAAS,cAAc,CAAC,qBAAqB;kEAC7D;;;;;;;;;;;;0DAIH,6WAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 4227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/RegistrationFormRouter.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { GuestRegistrationForm } from './GuestRegistrationForm'\nimport { ExhibitorRegistrationForm } from './ExhibitorRegistrationForm'\nimport { InvestorRegistrationForm } from './InvestorRegistrationForm'\n// Import other forms as we create them\n// import { VIPRegistrationForm } from './VIPRegistrationForm'\n// import { SponsorRegistrationForm } from './SponsorRegistrationForm'\n\n// Placeholder components for forms we haven't created yet\n\nfunction VIPRegistrationForm() {\n  return (\n    <div className=\"max-w-4xl mx-auto text-center space-y-4\">\n      <h1 className=\"text-3xl font-bold\">VIP Registration</h1>\n      <p className=\"text-muted-foreground\">\n        VIP registration form will be implemented here\n      </p>\n      <div className=\"bg-muted p-8 rounded-lg\">\n        <p className=\"text-sm text-muted-foreground\">\n          Form for invited guests, dignitaries, or keynote speakers\n        </p>\n      </div>\n    </div>\n  )\n}\n\nfunction SponsorRegistrationForm() {\n  return (\n    <div className=\"max-w-4xl mx-auto text-center space-y-4\">\n      <h1 className=\"text-3xl font-bold\">Sponsor Registration</h1>\n      <p className=\"text-muted-foreground\">\n        Sponsor registration form will be implemented here\n      </p>\n      <div className=\"bg-muted p-8 rounded-lg\">\n        <p className=\"text-sm text-muted-foreground\">\n          Form for organizations wanting to sponsor the conference\n        </p>\n      </div>\n    </div>\n  )\n}\n\nexport function RegistrationFormRouter() {\n  const { state } = useRegistration()\n\n  if (!state.data.userType) {\n    return (\n      <div className=\"max-w-4xl mx-auto text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-destructive\">Error</h1>\n        <p className=\"text-muted-foreground\">\n          No user type selected. Please go back and select a registration type.\n        </p>\n      </div>\n    )\n  }\n\n  switch (state.data.userType) {\n    case 'guest':\n      return <GuestRegistrationForm />\n    \n    case 'exhibitor':\n      return <ExhibitorRegistrationForm />\n    \n    case 'investor':\n      return <InvestorRegistrationForm />\n    \n    case 'vip':\n      return <VIPRegistrationForm />\n    \n    case 'sponsor':\n      return <SponsorRegistrationForm />\n    \n    default:\n      return (\n        <div className=\"max-w-4xl mx-auto text-center space-y-4\">\n          <h1 className=\"text-3xl font-bold text-destructive\">Error</h1>\n          <p className=\"text-muted-foreground\">\n            Unknown registration type: {state.data.userType}\n          </p>\n        </div>\n      )\n  }\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAOA,uCAAuC;AACvC,8DAA8D;AAC9D,sEAAsE;AAEtE,0DAA0D;AAE1D,SAAS;IACP,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,6WAAC;gBAAE,WAAU;0BAAwB;;;;;;0BAGrC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMrD;AAEA,SAAS;IACP,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,6WAAC;gBAAE,WAAU;0BAAwB;;;;;;0BAGrC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;;;;;;AAMrD;AAEO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEhC,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;QACxB,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAAsC;;;;;;8BACpD,6WAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,OAAQ,MAAM,IAAI,CAAC,QAAQ;QACzB,KAAK;YACH,qBAAO,6WAAC,oJAAA,CAAA,wBAAqB;;;;;QAE/B,KAAK;YACH,qBAAO,6WAAC,wJAAA,CAAA,4BAAyB;;;;;QAEnC,KAAK;YACH,qBAAO,6WAAC,uJAAA,CAAA,2BAAwB;;;;;QAElC,KAAK;YACH,qBAAO,6WAAC;;;;;QAEV,KAAK;YACH,qBAAO,6WAAC;;;;;QAEV;YACE,qBACE,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6WAAC;wBAAE,WAAU;;4BAAwB;4BACP,MAAM,IAAI,CAAC,QAAQ;;;;;;;;;;;;;IAIzD;AACF", "debugId": null}}, {"offset": {"line": 4424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/ReviewConfirmation.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTit<PERSON> } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Checkbox } from '@/components/ui/Checkbox'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { USER_TYPE_OPTIONS, COUNTRIES, INDUSTRIES, DIETARY_RESTRICTIONS, INTEREST_TRACKS } from '@/lib/constants'\nimport { formatCurrency } from '@/lib/utils'\nimport { Edit, Check, User, Building, Target, Utensils, DollarSign } from 'lucide-react'\n\nexport function ReviewConfirmation() {\n  const { state, goToStep, submitRegistration } = useRegistration()\n  const [agreedToTerms, setAgreedToTerms] = React.useState(false)\n\n  const userTypeOption = USER_TYPE_OPTIONS.find(option => option.type === state.data.userType)\n  const country = COUNTRIES.find(c => c.code === state.data.country)\n\n  const handleEdit = () => {\n    goToStep('details')\n  }\n\n  const handleSubmit = async () => {\n    if (agreedToTerms) {\n      await submitRegistration()\n    }\n  }\n\n  const renderPersonalInfo = () => (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <User className=\"w-5 h-5 text-primary-green\" />\n            <span>Personal Information</span>\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={handleEdit}>\n            <Edit className=\"w-4 h-4 mr-2\" />\n            Edit\n          </Button>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"text-sm font-medium text-muted-foreground\">Full Name</label>\n            <p className=\"text-foreground\">{state.data.fullName}</p>\n          </div>\n          <div>\n            <label className=\"text-sm font-medium text-muted-foreground\">Email</label>\n            <p className=\"text-foreground\">{state.data.email}</p>\n          </div>\n          <div>\n            <label className=\"text-sm font-medium text-muted-foreground\">Phone Number</label>\n            <p className=\"text-foreground\">{state.data.phoneNumber}</p>\n          </div>\n          <div>\n            <label className=\"text-sm font-medium text-muted-foreground\">Country</label>\n            <p className=\"text-foreground\">\n              {country ? `${country.flag} ${country.name}` : state.data.country}\n            </p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n\n  const renderRegistrationType = () => (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <Target className=\"w-5 h-5 text-primary-green\" />\n          <span>Registration Type</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"flex items-center space-x-4 p-4 bg-primary-green/5 rounded-lg border border-primary-green/20\">\n          <div className=\"text-2xl\">{userTypeOption?.icon}</div>\n          <div>\n            <h3 className=\"font-semibold text-foreground\">{userTypeOption?.title}</h3>\n            <p className=\"text-sm text-muted-foreground\">{userTypeOption?.description}</p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n\n  const renderGuestSpecific = () => {\n    if (state.data.userType !== 'guest') return null\n    \n    const guestData = state.data as any\n    \n    return (\n      <>\n        {guestData.organization && (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Building className=\"w-5 h-5 text-primary-green\" />\n                <span>Organization</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-foreground\">{guestData.organization}</p>\n            </CardContent>\n          </Card>\n        )}\n        \n        {guestData.dietaryRestrictions?.length > 0 && (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Utensils className=\"w-5 h-5 text-primary-green\" />\n                <span>Dietary Restrictions</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex flex-wrap gap-2\">\n                {guestData.dietaryRestrictions.map((restriction: string) => {\n                  const restrictionOption = DIETARY_RESTRICTIONS.find(r => r.value === restriction)\n                  return (\n                    <span key={restriction} className=\"px-3 py-1 bg-muted rounded-full text-sm\">\n                      {restrictionOption?.label || restriction}\n                    </span>\n                  )\n                })}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n        \n        {guestData.interestTracks?.length > 0 && (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Target className=\"w-5 h-5 text-primary-green\" />\n                <span>Interest Tracks</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {guestData.interestTracks.map((track: string) => {\n                  const trackOption = INTEREST_TRACKS.find(t => t.value === track)\n                  return (\n                    <div key={track} className=\"flex items-start space-x-2\">\n                      <Check className=\"w-4 h-4 text-primary-green mt-0.5\" />\n                      <div>\n                        <p className=\"font-medium text-sm\">{trackOption?.label || track}</p>\n                        {trackOption?.description && (\n                          <p className=\"text-xs text-muted-foreground\">{trackOption.description}</p>\n                        )}\n                      </div>\n                    </div>\n                  )\n                })}\n              </div>\n            </CardContent>\n          </Card>\n        )}\n      </>\n    )\n  }\n\n  const renderExhibitorSpecific = () => {\n    if (state.data.userType !== 'exhibitor') return null\n    \n    const exhibitorData = state.data as any\n    \n    return (\n      <>\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Building className=\"w-5 h-5 text-primary-green\" />\n              <span>Company Information</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Company Name</label>\n                <p className=\"text-foreground\">{exhibitorData.companyName}</p>\n              </div>\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Job Title</label>\n                <p className=\"text-foreground\">{exhibitorData.jobTitle}</p>\n              </div>\n              {exhibitorData.companyWebsite && (\n                <div>\n                  <label className=\"text-sm font-medium text-muted-foreground\">Website</label>\n                  <p className=\"text-foreground\">{exhibitorData.companyWebsite}</p>\n                </div>\n              )}\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Booth Size</label>\n                <p className=\"text-foreground capitalize\">{exhibitorData.boothSize}</p>\n              </div>\n            </div>\n            {exhibitorData.companyDescription && (\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Company Description</label>\n                <p className=\"text-foreground text-sm\">{exhibitorData.companyDescription}</p>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </>\n    )\n  }\n\n  const renderInvestorSpecific = () => {\n    if (state.data.userType !== 'investor') return null\n    \n    const investorData = state.data as any\n    \n    return (\n      <>\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <DollarSign className=\"w-5 h-5 text-primary-green\" />\n              <span>Investment Profile</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Company/Fund</label>\n                <p className=\"text-foreground\">{investorData.companyName}</p>\n              </div>\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Investor Type</label>\n                <p className=\"text-foreground\">{investorData.investorType}</p>\n              </div>\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Investment Range</label>\n                <p className=\"text-foreground\">{investorData.investmentRange}</p>\n              </div>\n              <div>\n                <label className=\"text-sm font-medium text-muted-foreground\">Available for Meetings</label>\n                <p className=\"text-foreground\">{investorData.availableForMeetings ? 'Yes' : 'No'}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </>\n    )\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-3xl font-bold text-foreground\">\n          Review Your Registration\n        </h1>\n        <p className=\"text-lg text-muted-foreground\">\n          Please review your information before submitting your registration\n        </p>\n      </div>\n\n      {/* Registration Details */}\n      <div className=\"space-y-6\">\n        {renderRegistrationType()}\n        {renderPersonalInfo()}\n        {renderGuestSpecific()}\n        {renderExhibitorSpecific()}\n        {renderInvestorSpecific()}\n      </div>\n\n      {/* Terms and Conditions */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <Checkbox\n            label=\"I agree to the Terms & Conditions and Privacy Policy\"\n            description=\"By checking this box, you agree to our terms of service and privacy policy\"\n            checked={agreedToTerms}\n            onChange={(e) => setAgreedToTerms(e.target.checked)}\n            required\n          />\n        </CardContent>\n      </Card>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-between items-center\">\n        <Button variant=\"outline\" onClick={handleEdit}>\n          <Edit className=\"w-4 h-4 mr-2\" />\n          Edit Information\n        </Button>\n        \n        <Button\n          size=\"lg\"\n          onClick={handleSubmit}\n          disabled={!agreedToTerms}\n          loading={state.isSubmitting}\n          className=\"min-w-[200px]\"\n        >\n          Submit Registration\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAWO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,oUAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,iBAAiB,uHAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ;IAC3F,MAAM,UAAU,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM,IAAI,CAAC,OAAO;IAEjE,MAAM,aAAa;QACjB,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,IAAI,eAAe;YACjB,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,kBACzB,6WAAC,gIAAA,CAAA,OAAI;;8BACH,6WAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6WAAC;kDAAK;;;;;;;;;;;;0CAER,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;;kDACzC,6WAAC,+RAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAKvC,6WAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;;kDACC,6WAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,6WAAC;wCAAE,WAAU;kDAAmB,MAAM,IAAI,CAAC,QAAQ;;;;;;;;;;;;0CAErD,6WAAC;;kDACC,6WAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,6WAAC;wCAAE,WAAU;kDAAmB,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;0CAElD,6WAAC;;kDACC,6WAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,6WAAC;wCAAE,WAAU;kDAAmB,MAAM,IAAI,CAAC,WAAW;;;;;;;;;;;;0CAExD,6WAAC;;kDACC,6WAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,6WAAC;wCAAE,WAAU;kDACV,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ7E,MAAM,yBAAyB,kBAC7B,6WAAC,gIAAA,CAAA,OAAI;;8BACH,6WAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6WAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6WAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CAAY,gBAAgB;;;;;;0CAC3C,6WAAC;;kDACC,6WAAC;wCAAG,WAAU;kDAAiC,gBAAgB;;;;;;kDAC/D,6WAAC;wCAAE,WAAU;kDAAiC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxE,MAAM,sBAAsB;QAC1B,IAAI,MAAM,IAAI,CAAC,QAAQ,KAAK,SAAS,OAAO;QAE5C,MAAM,YAAY,MAAM,IAAI;QAE5B,qBACE;;gBACG,UAAU,YAAY,kBACrB,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6WAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,6WAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,6WAAC;gCAAE,WAAU;0CAAmB,UAAU,YAAY;;;;;;;;;;;;;;;;;gBAK3D,UAAU,mBAAmB,EAAE,SAAS,mBACvC,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6WAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,6WAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,6WAAC;gCAAI,WAAU;0CACZ,UAAU,mBAAmB,CAAC,GAAG,CAAC,CAAC;oCAClC,MAAM,oBAAoB,uHAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oCACrE,qBACE,6WAAC;wCAAuB,WAAU;kDAC/B,mBAAmB,SAAS;uCADpB;;;;;gCAIf;;;;;;;;;;;;;;;;;gBAMP,UAAU,cAAc,EAAE,SAAS,mBAClC,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,6WAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,6WAAC;gCAAI,WAAU;0CACZ,UAAU,cAAc,CAAC,GAAG,CAAC,CAAC;oCAC7B,MAAM,cAAc,uHAAA,CAAA,kBAAe,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;oCAC1D,qBACE,6WAAC;wCAAgB,WAAU;;0DACzB,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6WAAC;;kEACC,6WAAC;wDAAE,WAAU;kEAAuB,aAAa,SAAS;;;;;;oDACzD,aAAa,6BACZ,6WAAC;wDAAE,WAAU;kEAAiC,YAAY,WAAW;;;;;;;;;;;;;uCALjE;;;;;gCAUd;;;;;;;;;;;;;;;;;;;IAOd;IAEA,MAAM,0BAA0B;QAC9B,IAAI,MAAM,IAAI,CAAC,QAAQ,KAAK,aAAa,OAAO;QAEhD,MAAM,gBAAgB,MAAM,IAAI;QAEhC,qBACE;sBACE,cAAA,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,6WAAC;gDAAE,WAAU;0DAAmB,cAAc,WAAW;;;;;;;;;;;;kDAE3D,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,6WAAC;gDAAE,WAAU;0DAAmB,cAAc,QAAQ;;;;;;;;;;;;oCAEvD,cAAc,cAAc,kBAC3B,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,6WAAC;gDAAE,WAAU;0DAAmB,cAAc,cAAc;;;;;;;;;;;;kDAGhE,6WAAC;;0DACC,6WAAC;gDAAM,WAAU;0DAA4C;;;;;;0DAC7D,6WAAC;gDAAE,WAAU;0DAA8B,cAAc,SAAS;;;;;;;;;;;;;;;;;;4BAGrE,cAAc,kBAAkB,kBAC/B,6WAAC;;kDACC,6WAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAC7D,6WAAC;wCAAE,WAAU;kDAA2B,cAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;IAOtF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,MAAM,IAAI,CAAC,QAAQ,KAAK,YAAY,OAAO;QAE/C,MAAM,eAAe,MAAM,IAAI;QAE/B,qBACE;sBACE,cAAA,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6WAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAAmB,aAAa,WAAW;;;;;;;;;;;;8CAE1D,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAAmB,aAAa,YAAY;;;;;;;;;;;;8CAE3D,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAAmB,aAAa,eAAe;;;;;;;;;;;;8CAE9D,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAAmB,aAAa,oBAAoB,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO1F;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAGnD,6WAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAM/C,6WAAC;gBAAI,WAAU;;oBACZ;oBACA;oBACA;oBACA;oBACA;;;;;;;0BAIH,6WAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC,oIAAA,CAAA,WAAQ;wBACP,OAAM;wBACN,aAAY;wBACZ,SAAS;wBACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;wBAClD,QAAQ;;;;;;;;;;;;;;;;0BAMd,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;0CACjC,6WAAC,+RAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAInC,6WAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,SAAS,MAAM,YAAY;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 5389, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/components/forms/RegistrationSuccess.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { useRegistration } from '@/context/RegistrationContext'\nimport { USER_TYPE_OPTIONS } from '@/lib/constants'\nimport { \n  CheckCircle, \n  Mail, \n  Calendar, \n  Download, \n  Share2, \n  Home, \n  Phone,\n  ExternalLink\n} from 'lucide-react'\n\nexport function RegistrationSuccess() {\n  const { state, resetRegistration } = useRegistration()\n  \n  const userTypeOption = USER_TYPE_OPTIONS.find(option => option.type === state.data.userType)\n  const registrationId = `IKIA2025-${Math.random().toString(36).substr(2, 9).toUpperCase()}`\n\n  const handleNewRegistration = () => {\n    resetRegistration()\n  }\n\n  const handleDownloadConfirmation = () => {\n    // In a real app, this would generate and download a PDF confirmation\n    console.log('Downloading confirmation...')\n  }\n\n  const handleShareRegistration = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: 'IKIA 2025 Registration Confirmed',\n        text: `I've registered for the 1st International Investment Conference on Indigenous Knowledge Intellectual Assets!`,\n        url: window.location.origin\n      })\n    } else {\n      // Fallback for browsers that don't support Web Share API\n      navigator.clipboard.writeText(window.location.origin)\n    }\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-8\">\n      {/* Success Header */}\n      <div className=\"text-center space-y-6\">\n        <div className=\"flex justify-center\">\n          <div className=\"w-20 h-20 bg-success rounded-full flex items-center justify-center\">\n            <CheckCircle className=\"w-12 h-12 text-white\" />\n          </div>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <h1 className=\"text-4xl font-bold text-success\">\n            Registration Successful!\n          </h1>\n          <p className=\"text-xl text-muted-foreground\">\n            Thank you for registering for IKIA 2025\n          </p>\n        </div>\n      </div>\n\n      {/* Registration Confirmation */}\n      <Card className=\"border-success/20 bg-success/5\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2 text-success\">\n            <CheckCircle className=\"w-5 h-5\" />\n            <span>Registration Confirmed</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"text-sm font-medium text-muted-foreground\">Registration ID</label>\n              <p className=\"text-lg font-mono text-foreground\">{registrationId}</p>\n            </div>\n            <div>\n              <label className=\"text-sm font-medium text-muted-foreground\">Registration Type</label>\n              <p className=\"text-lg text-foreground\">\n                {userTypeOption?.icon} {userTypeOption?.title}\n              </p>\n            </div>\n            <div>\n              <label className=\"text-sm font-medium text-muted-foreground\">Registered Name</label>\n              <p className=\"text-lg text-foreground\">{state.data.fullName}</p>\n            </div>\n            <div>\n              <label className=\"text-sm font-medium text-muted-foreground\">Email Address</label>\n              <p className=\"text-lg text-foreground\">{state.data.email}</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Next Steps */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Calendar className=\"w-5 h-5 text-primary-green\" />\n            <span>What Happens Next?</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                1\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-foreground\">Confirmation Email</h4>\n                <p className=\"text-sm text-muted-foreground\">\n                  A confirmation email has been sent to <strong>{state.data.email}</strong>. \n                  Please check your spam folder if you don't see it within 10 minutes.\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                2\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-foreground\">Event Details</h4>\n                <p className=\"text-sm text-muted-foreground\">\n                  You'll receive detailed event information including venue, schedule, \n                  and access instructions closer to the conference date.\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                3\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-foreground\">Attendee Dashboard</h4>\n                <p className=\"text-sm text-muted-foreground\">\n                  Access your personalized attendee dashboard to view your schedule, \n                  connect with other participants, and access conference materials.\n                </p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Button \n          variant=\"outline\" \n          className=\"h-auto p-4 flex flex-col items-center space-y-2\"\n          onClick={handleDownloadConfirmation}\n        >\n          <Download className=\"w-6 h-6\" />\n          <span className=\"text-sm\">Download Confirmation</span>\n        </Button>\n        \n        <Button \n          variant=\"outline\" \n          className=\"h-auto p-4 flex flex-col items-center space-y-2\"\n          onClick={handleShareRegistration}\n        >\n          <Share2 className=\"w-6 h-6\" />\n          <span className=\"text-sm\">Share Registration</span>\n        </Button>\n        \n        <Button \n          variant=\"outline\" \n          className=\"h-auto p-4 flex flex-col items-center space-y-2\"\n        >\n          <Calendar className=\"w-6 h-6\" />\n          <span className=\"text-sm\">Add to Calendar</span>\n        </Button>\n        \n        <Button \n          variant=\"outline\" \n          className=\"h-auto p-4 flex flex-col items-center space-y-2\"\n        >\n          <ExternalLink className=\"w-6 h-6\" />\n          <span className=\"text-sm\">View Agenda</span>\n        </Button>\n      </div>\n\n      {/* Contact Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Phone className=\"w-5 h-5 text-primary-green\" />\n            <span>Need Help?</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <p className=\"text-muted-foreground\">\n            If you have any questions about your registration or the conference, \n            please don't hesitate to contact our support team.\n          </p>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"flex items-center space-x-3\">\n              <Mail className=\"w-5 h-5 text-primary-green\" />\n              <div>\n                <p className=\"font-medium text-foreground\">Email Support</p>\n                <p className=\"text-sm text-muted-foreground\"><EMAIL></p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              <Phone className=\"w-5 h-5 text-primary-green\" />\n              <div>\n                <p className=\"font-medium text-foreground\">Phone Support</p>\n                <p className=\"text-sm text-muted-foreground\">+254 (0) 700 123 456</p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n        <Button \n          size=\"lg\"\n          onClick={() => window.location.href = '/'}\n          className=\"flex items-center space-x-2\"\n        >\n          <Home className=\"w-4 h-4\" />\n          <span>Return to Home</span>\n        </Button>\n        \n        <Button \n          variant=\"outline\" \n          size=\"lg\"\n          onClick={handleNewRegistration}\n          className=\"flex items-center space-x-2\"\n        >\n          <span>Register Another Person</span>\n        </Button>\n      </div>\n\n      {/* Social Sharing */}\n      <div className=\"text-center space-y-4 pt-8 border-t border-border\">\n        <h3 className=\"text-lg font-semibold text-foreground\">\n          Spread the Word!\n        </h3>\n        <p className=\"text-muted-foreground\">\n          Help us make IKIA 2025 a success by sharing with your network\n        </p>\n        <div className=\"flex justify-center space-x-4\">\n          <Button variant=\"outline\" size=\"sm\">Share on LinkedIn</Button>\n          <Button variant=\"outline\" size=\"sm\">Share on Twitter</Button>\n          <Button variant=\"outline\" size=\"sm\">Share on Facebook</Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEnD,MAAM,iBAAiB,uHAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,QAAQ;IAC3F,MAAM,iBAAiB,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,IAAI;IAE1F,MAAM,wBAAwB;QAC5B;IACF;IAEA,MAAM,6BAA6B;QACjC,qEAAqE;QACrE,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,0BAA0B;QAC9B,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,MAAM,CAAC,4GAA4G,CAAC;gBACpH,KAAK,OAAO,QAAQ,CAAC,MAAM;YAC7B;QACF,OAAO;YACL,yDAAyD;YACzD,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,MAAM;QACtD;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,+SAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAI3B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAGhD,6WAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAOjD,6WAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,+SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6WAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;;gDACV,gBAAgB;gDAAK;gDAAE,gBAAgB;;;;;;;;;;;;;8CAG5C,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAA2B,MAAM,IAAI,CAAC,QAAQ;;;;;;;;;;;;8CAE7D,6WAAC;;sDACC,6WAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6WAAC;4CAAE,WAAU;sDAA2B,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6WAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDAAsG;;;;;;sDAGrH,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6WAAC;oDAAE,WAAU;;wDAAgC;sEACL,6WAAC;sEAAQ,MAAM,IAAI,CAAC,KAAK;;;;;;wDAAU;;;;;;;;;;;;;;;;;;;8CAM/E,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDAAsG;;;;;;sDAGrH,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6WAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAOjD,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDAAsG;;;;;;sDAGrH,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6WAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvD,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;;0CAET,6WAAC,8RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,6WAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;;0CAET,6WAAC,8RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,6WAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;;0CAEV,6WAAC,8RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,6WAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;;0CAEV,6WAAC,0SAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6WAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;0BAK9B,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6WAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAKrC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6WAAC;;kEACC,6WAAC;wDAAE,WAAU;kEAA8B;;;;;;kEAC3C,6WAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAIjD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6WAAC;;kEACC,6WAAC;wDAAE,WAAU;kEAA8B;;;;;;kEAC3C,6WAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvD,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACtC,WAAU;;0CAEV,6WAAC,uRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6WAAC;0CAAK;;;;;;;;;;;;kCAGR,6WAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,6WAAC;sCAAK;;;;;;;;;;;;;;;;;0BAKV,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6WAAC;wBAAE,WAAU;kCAAwB;;;;;;kCAGrC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;0CACpC,6WAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 6210, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/task/registration/src/app/registration/page.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { RegistrationProvider, useRegistration } from '@/context/RegistrationContext'\nimport { RegistrationLayout } from '@/components/forms/RegistrationLayout'\nimport { UserTypeSelection } from '@/components/forms/UserTypeSelection'\nimport { RegistrationFormRouter } from '@/components/forms/RegistrationFormRouter'\nimport { ReviewConfirmation } from '@/components/forms/ReviewConfirmation'\nimport { RegistrationSuccess } from '@/components/forms/RegistrationSuccess'\n\n// Details step now uses the form router\nfunction DetailsStep() {\n  return <RegistrationFormRouter />\n}\n\nfunction ReviewStep() {\n  return <ReviewConfirmation />\n}\n\nfunction SuccessStep() {\n  return <RegistrationSuccess />\n}\n\nfunction RegistrationContent() {\n  const { state } = useRegistration()\n\n  const renderStep = () => {\n    switch (state.currentStep) {\n      case 'user-type':\n        return <UserTypeSelection />\n      case 'details':\n        return <DetailsStep />\n      case 'review':\n        return <ReviewStep />\n      case 'success':\n        return <SuccessStep />\n      default:\n        return <UserTypeSelection />\n    }\n  }\n\n  return (\n    <RegistrationLayout>\n      {renderStep()}\n    </RegistrationLayout>\n  )\n}\n\nexport default function RegistrationPage() {\n  return (\n    <RegistrationProvider>\n      <RegistrationContent />\n    </RegistrationProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,wCAAwC;AACxC,SAAS;IACP,qBAAO,6WAAC,qJAAA,CAAA,yBAAsB;;;;;AAChC;AAEA,SAAS;IACP,qBAAO,6WAAC,iJAAA,CAAA,qBAAkB;;;;;AAC5B;AAEA,SAAS;IACP,qBAAO,6WAAC,kJAAA,CAAA,sBAAmB;;;;;AAC7B;AAEA,SAAS;IACP,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEhC,MAAM,aAAa;QACjB,OAAQ,MAAM,WAAW;YACvB,KAAK;gBACH,qBAAO,6WAAC,gJAAA,CAAA,oBAAiB;;;;;YAC3B,KAAK;gBACH,qBAAO,6WAAC;;;;;YACV,KAAK;gBA<PERSON>,qBAAO,6WAAC;;;;;YACV,KAAK;gBACH,qBAAO,6WAAC;;;;;YACV;gBACE,qBAAO,6WAAC,gJAAA,CAAA,oBAAiB;;;;;QAC7B;IACF;IAEA,qBACE,6WAAC,iJAAA,CAAA,qBAAkB;kBAChB;;;;;;AAGP;AAEe,SAAS;IACtB,qBACE,6WAAC,sIAAA,CAAA,uBAAoB;kBACnB,cAAA,6WAAC;;;;;;;;;;AAGP", "debugId": null}}]}