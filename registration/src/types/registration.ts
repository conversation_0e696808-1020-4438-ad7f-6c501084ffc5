export type UserType = 'guest' | 'exhibitor' | 'investor' | 'vip' | 'sponsor'

export type RegistrationStep = 
  | 'user-type'
  | 'details'
  | 'review'
  | 'success'

export interface BaseRegistrationData {
  userType: UserType
  fullName: string
  email: string
  phoneNumber: string
  country: string
}

export interface GuestRegistrationData extends BaseRegistrationData {
  userType: 'guest'
  organization?: string
  dietaryRestrictions: string[]
  accessibilityNeeds?: string
  interestTracks: string[]
}

export interface ExhibitorRegistrationData extends BaseRegistrationData {
  userType: 'exhibitor'
  jobTitle: string
  companyName: string
  companyWebsite?: string
  companyDescription: string
  primaryIndustry: string
  productsServices: string
  companyLogo?: File
  companyProfile?: File
  boothSize: 'standard' | 'premium' | 'custom'
  powerRequirements: string[]
  staffCount: number
}

export interface InvestorRegistrationData extends BaseRegistrationData {
  userType: 'investor'
  companyName: string
  companyWebsite?: string
  investorType: string
  investmentSectors: string[]
  investmentRange: string
  geographicFocus: string[]
  ikiaInterests?: string
  availableForMeetings: boolean
  investorProfile?: File
}

export interface VIPRegistrationData extends BaseRegistrationData {
  userType: 'vip'
  organization: string
  jobTitle: string
  invitationCode?: string
  dietaryRestrictions: string[]
  accessibilityNeeds?: string
}

export interface SponsorRegistrationData extends BaseRegistrationData {
  userType: 'sponsor'
  jobTitle: string
  companyName: string
  companyWebsite?: string
  companyDescription: string
  companyIndustry: string
  companyCountry: string
  companyLogo?: File
  sponsorshipTier: 'platinum' | 'gold' | 'silver' | 'bronze'
  paymentMethod: string
  specialRequests?: string
  agreedToTerms: boolean
}

export type RegistrationData = 
  | GuestRegistrationData
  | ExhibitorRegistrationData
  | InvestorRegistrationData
  | VIPRegistrationData
  | SponsorRegistrationData

export interface RegistrationState {
  currentStep: RegistrationStep
  data: Partial<RegistrationData>
  errors: Record<string, string>
  isSubmitting: boolean
}

export interface RegistrationContextType {
  state: RegistrationState
  updateData: (data: Partial<RegistrationData>) => void
  nextStep: () => void
  prevStep: () => void
  goToStep: (step: RegistrationStep) => void
  setError: (field: string, error: string) => void
  clearError: (field: string) => void
  clearAllErrors: () => void
  submitRegistration: () => Promise<void>
  resetRegistration: () => void
}

export interface UserTypeOption {
  type: UserType
  title: string
  description: string
  icon: string
  features: string[]
}

export interface FormFieldProps {
  label: string
  name: string
  type?: string
  placeholder?: string
  required?: boolean
  error?: string
  helpText?: string
  className?: string
}

export interface SelectOption {
  value: string
  label: string
}

export interface FileUploadProps {
  label: string
  name: string
  accept?: string
  maxSize?: number
  required?: boolean
  error?: string
  helpText?: string
  className?: string
}

export interface ProgressIndicatorProps {
  currentStep: RegistrationStep
  userType?: UserType
}

export interface SponsorshipTier {
  id: 'platinum' | 'gold' | 'silver' | 'bronze'
  name: string
  amount: number
  currency: string
  benefits: string[]
  highlighted?: boolean
}

export interface CountryOption {
  code: string
  name: string
  flag: string
}

export interface IndustryOption {
  value: string
  label: string
  category?: string
}

export interface DietaryRestriction {
  value: string
  label: string
}

export interface InterestTrack {
  value: string
  label: string
  description?: string
}

export interface ValidationError {
  field: string
  message: string
}

export interface RegistrationResponse {
  success: boolean
  registrationId?: string
  message: string
  errors?: ValidationError[]
}
