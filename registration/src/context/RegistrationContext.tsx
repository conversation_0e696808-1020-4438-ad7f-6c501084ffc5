'use client'

import React, { createContext, useContext, useReducer, ReactNode } from 'react'
import { 
  RegistrationState, 
  RegistrationContextType, 
  RegistrationData, 
  RegistrationStep,
  UserType 
} from '@/types/registration'

// Initial state
const initialState: RegistrationState = {
  currentStep: 'user-type',
  data: {},
  errors: {},
  isSubmitting: false,
}

// Action types
type RegistrationAction =
  | { type: 'UPDATE_DATA'; payload: Partial<RegistrationData> }
  | { type: 'NEXT_STEP' }
  | { type: 'PREV_STEP' }
  | { type: 'GO_TO_STEP'; payload: RegistrationStep }
  | { type: 'SET_ERROR'; payload: { field: string; error: string } }
  | { type: 'CLEAR_ERROR'; payload: string }
  | { type: 'CLEAR_ALL_ERRORS' }
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'RESET_REGISTRATION' }

// Step order mapping
const getStepOrder = (userType?: UserType): RegistrationStep[] => {
  return ['user-type', 'details', 'review', 'success']
}

// Reducer function
function registrationReducer(
  state: RegistrationState,
  action: RegistrationAction
): RegistrationState {
  switch (action.type) {
    case 'UPDATE_DATA':
      return {
        ...state,
        data: { ...state.data, ...action.payload },
      }

    case 'NEXT_STEP': {
      const stepOrder = getStepOrder(state.data.userType)
      const currentIndex = stepOrder.indexOf(state.currentStep)
      const nextIndex = Math.min(currentIndex + 1, stepOrder.length - 1)
      return {
        ...state,
        currentStep: stepOrder[nextIndex],
      }
    }

    case 'PREV_STEP': {
      const stepOrder = getStepOrder(state.data.userType)
      const currentIndex = stepOrder.indexOf(state.currentStep)
      const prevIndex = Math.max(currentIndex - 1, 0)
      return {
        ...state,
        currentStep: stepOrder[prevIndex],
      }
    }

    case 'GO_TO_STEP':
      return {
        ...state,
        currentStep: action.payload,
      }

    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.field]: action.payload.error,
        },
      }

    case 'CLEAR_ERROR': {
      const { [action.payload]: _, ...remainingErrors } = state.errors
      return {
        ...state,
        errors: remainingErrors,
      }
    }

    case 'CLEAR_ALL_ERRORS':
      return {
        ...state,
        errors: {},
      }

    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.payload,
      }

    case 'RESET_REGISTRATION':
      return initialState

    default:
      return state
  }
}

// Create context
const RegistrationContext = createContext<RegistrationContextType | undefined>(undefined)

// Provider component
interface RegistrationProviderProps {
  children: ReactNode
}

export function RegistrationProvider({ children }: RegistrationProviderProps) {
  const [state, dispatch] = useReducer(registrationReducer, initialState)

  const updateData = (data: Partial<RegistrationData>) => {
    dispatch({ type: 'UPDATE_DATA', payload: data })
  }

  const nextStep = () => {
    dispatch({ type: 'NEXT_STEP' })
  }

  const prevStep = () => {
    dispatch({ type: 'PREV_STEP' })
  }

  const goToStep = (step: RegistrationStep) => {
    dispatch({ type: 'GO_TO_STEP', payload: step })
  }

  const setError = (field: string, error: string) => {
    dispatch({ type: 'SET_ERROR', payload: { field, error } })
  }

  const clearError = (field: string) => {
    dispatch({ type: 'CLEAR_ERROR', payload: field })
  }

  const clearAllErrors = () => {
    dispatch({ type: 'CLEAR_ALL_ERRORS' })
  }

  const submitRegistration = async () => {
    dispatch({ type: 'SET_SUBMITTING', payload: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real app, you would make an API call here
      console.log('Submitting registration:', state.data)
      
      // Move to success step
      dispatch({ type: 'GO_TO_STEP', payload: 'success' })
    } catch (error) {
      console.error('Registration failed:', error)
      setError('general', 'Registration failed. Please try again.')
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false })
    }
  }

  const resetRegistration = () => {
    dispatch({ type: 'RESET_REGISTRATION' })
  }

  const contextValue: RegistrationContextType = {
    state,
    updateData,
    nextStep,
    prevStep,
    goToStep,
    setError,
    clearError,
    clearAllErrors,
    submitRegistration,
    resetRegistration,
  }

  return (
    <RegistrationContext.Provider value={contextValue}>
      {children}
    </RegistrationContext.Provider>
  )
}

// Hook to use the registration context
export function useRegistration() {
  const context = useContext(RegistrationContext)
  if (context === undefined) {
    throw new Error('useRegistration must be used within a RegistrationProvider')
  }
  return context
}
