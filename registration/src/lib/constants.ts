import { UserTypeOption, SponsorshipTier, CountryOption, IndustryOption, DietaryRestriction, InterestTrack } from '@/types/registration'

export const USER_TYPE_OPTIONS: UserTypeOption[] = [
  {
    type: 'guest',
    title: 'Guest/Attendee',
    description: 'Attending the conference and trade fair sessions',
    icon: '👤',
    features: [
      'Access to all conference sessions',
      'Networking opportunities',
      'Exhibition hall access',
      'Conference materials',
      'Refreshments and meals'
    ]
  },
  {
    type: 'exhibitor',
    title: 'Exhibitor',
    description: 'Showcasing your products or services at the trade fair',
    icon: '🏢',
    features: [
      'Dedicated exhibition space',
      'Company branding opportunities',
      'Lead generation tools',
      'Networking sessions',
      'Marketing materials inclusion'
    ]
  },
  {
    type: 'investor',
    title: 'Investor',
    description: 'Looking to explore investment opportunities in IKIAs',
    icon: '💼',
    features: [
      'Exclusive investor sessions',
      'One-on-one meetings',
      'Investment pitch access',
      'Due diligence materials',
      'VIP networking events'
    ]
  },
  {
    type: 'vip',
    title: 'VIP',
    description: 'Invited guests, dignitaries, or keynote speakers',
    icon: '⭐',
    features: [
      'VIP lounge access',
      'Priority seating',
      'Exclusive networking',
      'Special recognition',
      'Premium amenities'
    ]
  },
  {
    type: 'sponsor',
    title: 'Sponsor',
    description: 'Supporting the conference through sponsorship',
    icon: '🤝',
    features: [
      'Brand visibility',
      'Speaking opportunities',
      'Networking privileges',
      'Marketing benefits',
      'Recognition and awards'
    ]
  }
]

export const SPONSORSHIP_TIERS: SponsorshipTier[] = [
  {
    id: 'platinum',
    name: 'Platinum Sponsor',
    amount: 5000000,
    currency: 'KES',
    highlighted: true,
    benefits: [
      'Prominent logo placement on all conference materials',
      'Keynote speaking slot (15-20 minutes) on main stage',
      'Dedicated large exhibition space (6x6m) in prime location',
      '10 VIP delegate passes with full access',
      'Exclusive branding of specific high-visibility conference area',
      'Full-page advertisement in official conference program',
      'Pre- and post-event attendee list (opt-in)',
      'Dedicated social media campaign and email blast',
      'Opportunity to host private side event/workshop'
    ]
  },
  {
    id: 'gold',
    name: 'Gold Sponsor',
    amount: 2500000,
    currency: 'KES',
    benefits: [
      'Large logo placement on most conference materials',
      'Speaking opportunity in panel discussion or breakout session',
      'Standard exhibition space (3x3m)',
      '5 VIP delegate passes with full access',
      'Branding of specific session, workshop, or coffee break',
      'Half-page advertisement in official conference program',
      'Social media mentions'
    ]
  },
  {
    id: 'silver',
    name: 'Silver Sponsor',
    amount: 1000000,
    currency: 'KES',
    benefits: [
      'Medium logo placement on select conference materials',
      'Shared exhibition space (2x2m tabletop)',
      '3 delegate passes',
      'Logo inclusion on conference website sponsor page',
      'Quarter-page advertisement in official conference program'
    ]
  },
  {
    id: 'bronze',
    name: 'Bronze Sponsor',
    amount: 500000,
    currency: 'KES',
    benefits: [
      'Small logo placement on conference website sponsor page',
      '1 delegate pass',
      'Mention in official conference program'
    ]
  }
]

export const COUNTRIES: CountryOption[] = [
  { code: 'KE', name: 'Kenya', flag: '🇰🇪' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭' },
  { code: 'US', name: 'United States', flag: '🇺🇸' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪' },
  { code: 'FR', name: 'France', flag: '🇫🇷' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵' },
  { code: 'CN', name: 'China', flag: '🇨🇳' },
  { code: 'IN', name: 'India', flag: '🇮🇳' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷' }
]

export const INDUSTRIES: IndustryOption[] = [
  { value: 'agritech', label: 'Agriculture & Technology', category: 'Technology' },
  { value: 'clean-energy', label: 'Clean Energy & Renewables', category: 'Energy' },
  { value: 'fintech', label: 'Financial Technology', category: 'Technology' },
  { value: 'cultural-tourism', label: 'Cultural Tourism', category: 'Tourism' },
  { value: 'traditional-medicine', label: 'Traditional Medicine', category: 'Healthcare' },
  { value: 'arts-crafts', label: 'Arts & Crafts', category: 'Creative' },
  { value: 'sustainable-development', label: 'Sustainable Development', category: 'Development' },
  { value: 'biotechnology', label: 'Biotechnology', category: 'Technology' },
  { value: 'education', label: 'Education & Training', category: 'Education' },
  { value: 'manufacturing', label: 'Manufacturing', category: 'Industry' },
  { value: 'government', label: 'Government & Public Sector', category: 'Public' },
  { value: 'ngo', label: 'Non-Governmental Organization', category: 'Non-Profit' },
  { value: 'consulting', label: 'Consulting Services', category: 'Services' },
  { value: 'media', label: 'Media & Communications', category: 'Media' },
  { value: 'other', label: 'Other', category: 'Other' }
]

export const DIETARY_RESTRICTIONS: DietaryRestriction[] = [
  { value: 'vegetarian', label: 'Vegetarian' },
  { value: 'vegan', label: 'Vegan' },
  { value: 'gluten-free', label: 'Gluten-Free' },
  { value: 'halal', label: 'Halal' },
  { value: 'kosher', label: 'Kosher' },
  { value: 'dairy-free', label: 'Dairy-Free' },
  { value: 'nut-free', label: 'Nut-Free' },
  { value: 'none', label: 'No Restrictions' }
]

export const INTEREST_TRACKS: InterestTrack[] = [
  { 
    value: 'agritech', 
    label: 'Agri-tech Innovation',
    description: 'Technology solutions for agriculture and food security'
  },
  { 
    value: 'clean-energy', 
    label: 'Clean Energy Solutions',
    description: 'Renewable energy and sustainable power systems'
  },
  { 
    value: 'fintech', 
    label: 'Financial Technology',
    description: 'Digital financial services and inclusion'
  },
  { 
    value: 'cultural-tourism', 
    label: 'Cultural Tourism',
    description: 'Heritage-based tourism and cultural preservation'
  },
  { 
    value: 'policy-governance', 
    label: 'Policy & Governance',
    description: 'Regulatory frameworks and governance structures'
  },
  { 
    value: 'ip-protection', 
    label: 'IP Protection',
    description: 'Intellectual property rights and protection mechanisms'
  },
  { 
    value: 'traditional-medicine', 
    label: 'Traditional Medicine',
    description: 'Indigenous healing practices and modern integration'
  },
  { 
    value: 'sustainable-development', 
    label: 'Sustainable Development',
    description: 'Environmental sustainability and community development'
  }
]

export const INVESTOR_TYPES = [
  { value: 'angel', label: 'Angel Investor' },
  { value: 'vc', label: 'Venture Capital' },
  { value: 'pe', label: 'Private Equity' },
  { value: 'corporate-vc', label: 'Corporate VC' },
  { value: 'family-office', label: 'Family Office' },
  { value: 'fund-manager', label: 'Fund Manager' },
  { value: 'impact-investor', label: 'Impact Investor' },
  { value: 'government', label: 'Government Fund' },
  { value: 'other', label: 'Other' }
]

export const INVESTMENT_RANGES = [
  { value: 'under-100k', label: 'Under $100K' },
  { value: '100k-500k', label: '$100K - $500K' },
  { value: '500k-1m', label: '$500K - $1M' },
  { value: '1m-5m', label: '$1M - $5M' },
  { value: '5m-10m', label: '$5M - $10M' },
  { value: 'over-10m', label: 'Over $10M' }
]

export const BOOTH_SIZES = [
  { value: 'standard', label: 'Standard (3x3m)', description: 'Basic booth space with standard amenities' },
  { value: 'premium', label: 'Premium (6x6m)', description: 'Larger space with enhanced features' },
  { value: 'custom', label: 'Custom Size', description: 'Tailored booth size based on requirements' }
]

export const POWER_REQUIREMENTS = [
  { value: 'standard', label: 'Standard Outlet (220V)' },
  { value: 'high-power', label: 'High Power (Industrial)' },
  { value: 'none', label: 'No Power Required' }
]

export const PAYMENT_METHODS = [
  { value: 'bank-transfer', label: 'Bank Transfer' },
  { value: 'credit-card', label: 'Credit Card' },
  { value: 'mobile-money', label: 'Mobile Money (M-Pesa)' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'other', label: 'Other (Please specify)' }
]
