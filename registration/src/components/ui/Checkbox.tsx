import React from 'react'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  description?: string
  error?: string
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ 
    className, 
    label,
    description,
    error,
    id,
    checked,
    ...props 
  }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`
    
    return (
      <div className="space-y-2">
        <div className="flex items-start space-x-3">
          <div className="relative flex items-center">
            <input
              type="checkbox"
              className="sr-only"
              ref={ref}
              id={checkboxId}
              checked={checked}
              {...props}
            />
            <div
              className={cn(
                'flex h-5 w-5 items-center justify-center rounded border-2 transition-colors duration-200',
                'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
                checked 
                  ? 'bg-primary-green border-primary-green text-white' 
                  : 'border-border bg-input hover:border-primary-green/50',
                error && 'border-destructive',
                props.disabled && 'opacity-50 cursor-not-allowed',
                className
              )}
            >
              {checked && (
                <Check className="h-3 w-3 text-white" strokeWidth={3} />
              )}
            </div>
          </div>
          
          {(label || description) && (
            <div className="flex-1 space-y-1">
              {label && (
                <label 
                  htmlFor={checkboxId}
                  className={cn(
                    'text-sm font-medium leading-none cursor-pointer',
                    props.disabled && 'cursor-not-allowed opacity-70'
                  )}
                >
                  {label}
                  {props.required && <span className="text-destructive ml-1">*</span>}
                </label>
              )}
              
              {description && (
                <p className="text-sm text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-destructive font-medium ml-8">
            {error}
          </p>
        )}
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'

export { Checkbox }
