import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  children: React.ReactNode
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    loading = false,
    disabled,
    children, 
    ...props 
  }, ref) => {
    const baseStyles = [
      'inline-flex items-center justify-center rounded-lg font-medium',
      'transition-colors duration-200 focus-visible:outline-none',
      'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50'
    ]

    const variants = {
      primary: [
        'bg-primary-green text-white hover:bg-primary-green/90',
        'shadow-sm hover:shadow-md'
      ],
      secondary: [
        'bg-secondary-sienna text-white hover:bg-secondary-sienna/90',
        'shadow-sm hover:shadow-md'
      ],
      outline: [
        'border border-border bg-background hover:bg-muted',
        'text-foreground hover:text-foreground'
      ],
      ghost: [
        'hover:bg-muted hover:text-foreground',
        'text-muted-foreground'
      ],
      destructive: [
        'bg-destructive text-destructive-foreground',
        'hover:bg-destructive/90 shadow-sm hover:shadow-md'
      ]
    }

    const sizes = {
      sm: 'h-9 px-3 text-sm',
      md: 'h-11 px-6 text-base',
      lg: 'h-12 px-8 text-lg'
    }

    return (
      <button
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
