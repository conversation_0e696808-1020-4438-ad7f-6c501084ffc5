import React from 'react'
import { cn } from '@/lib/utils'

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helpText?: string
  maxLength?: number
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    className, 
    label,
    error,
    helpText,
    maxLength,
    id,
    value,
    ...props 
  }, ref) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`
    const currentLength = typeof value === 'string' ? value.length : 0
    
    const textareaStyles = [
      'flex min-h-[80px] w-full rounded-lg border border-border bg-input px-3 py-2',
      'text-base ring-offset-background placeholder:text-muted-foreground',
      'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
      'focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      'transition-colors duration-200 resize-vertical'
    ]

    const errorStyles = error ? [
      'border-destructive focus-visible:ring-destructive'
    ] : []

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={textareaId}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {props.required && <span className="text-destructive ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          <textarea
            className={cn(
              textareaStyles,
              errorStyles,
              className
            )}
            ref={ref}
            id={textareaId}
            value={value}
            maxLength={maxLength}
            {...props}
          />
          
          {maxLength && (
            <div className="absolute bottom-2 right-2 text-xs text-muted-foreground bg-background px-1 rounded">
              {currentLength}/{maxLength}
            </div>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-destructive font-medium">
            {error}
          </p>
        )}
        
        {helpText && !error && (
          <p className="text-sm text-muted-foreground">
            {helpText}
          </p>
        )}
      </div>
    )
  }
)

Textarea.displayName = 'Textarea'

export { Textarea }
