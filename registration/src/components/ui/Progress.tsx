import React from 'react'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'

export interface ProgressStep {
  id: string
  title: string
  description?: string
  status: 'completed' | 'current' | 'upcoming'
}

export interface ProgressProps {
  steps: ProgressStep[]
  className?: string
}

const Progress: React.FC<ProgressProps> = ({ steps, className }) => {
  return (
    <nav aria-label="Progress" className={cn('w-full', className)}>
      <ol className="flex items-center justify-between w-full">
        {steps.map((step, stepIdx) => (
          <li key={step.id} className="flex-1 relative">
            <div className="flex items-center">
              {/* Step Circle */}
              <div className="flex items-center justify-center relative">
                <div
                  className={cn(
                    'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors duration-200',
                    step.status === 'completed' && 'bg-primary-green border-primary-green text-white',
                    step.status === 'current' && 'bg-primary-green border-primary-green text-white',
                    step.status === 'upcoming' && 'bg-background border-border text-muted-foreground'
                  )}
                >
                  {step.status === 'completed' ? (
                    <Check className="w-5 h-5" strokeWidth={2.5} />
                  ) : (
                    <span className="text-sm font-medium">{stepIdx + 1}</span>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {stepIdx < steps.length - 1 && (
                <div className="flex-1 ml-4">
                  <div
                    className={cn(
                      'h-0.5 w-full transition-colors duration-200',
                      step.status === 'completed' ? 'bg-primary-green' : 'bg-border'
                    )}
                  />
                </div>
              )}
            </div>

            {/* Step Label */}
            <div className="mt-3 text-center">
              <div
                className={cn(
                  'text-sm font-medium transition-colors duration-200',
                  step.status === 'current' && 'text-primary-green',
                  step.status === 'completed' && 'text-foreground',
                  step.status === 'upcoming' && 'text-muted-foreground'
                )}
              >
                {step.title}
              </div>
              {step.description && (
                <div className="text-xs text-muted-foreground mt-1">
                  {step.description}
                </div>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  )
}

Progress.displayName = 'Progress'

export { Progress }
