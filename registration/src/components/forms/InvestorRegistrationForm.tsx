'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Checkbox } from '@/components/ui/Checkbox'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { COUNTRIES, INVESTOR_TYPES, INVESTMENT_RANGES, INTEREST_TRACKS } from '@/lib/constants'
import { InvestorRegistrationData } from '@/types/registration'
import { User, Mail, Phone, Globe, Building, DollarSign, Target, Calendar, Upload } from 'lucide-react'

const investorSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),
  country: z.string().min(1, 'Please select your country'),
  companyName: z.string().min(2, 'Company/Fund name is required'),
  companyWebsite: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  investorType: z.string().min(1, 'Please select your investor type'),
  investmentSectors: z.array(z.string()).min(1, 'Please select at least one investment sector'),
  investmentRange: z.string().min(1, 'Please select your typical investment range'),
  geographicFocus: z.array(z.string()).min(1, 'Please select at least one geographic focus'),
  ikiaInterests: z.string().optional(),
  availableForMeetings: z.boolean().default(false),
})

type InvestorFormData = z.infer<typeof investorSchema>

const GEOGRAPHIC_FOCUS_OPTIONS = [
  { value: 'local', label: 'Local (Same Country)' },
  { value: 'regional', label: 'Regional (East Africa)' },
  { value: 'continental', label: 'Continental (Africa)' },
  { value: 'international', label: 'International (Global)' },
]

export function InvestorRegistrationForm() {
  const { state, updateData, nextStep } = useRegistration()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    trigger
  } = useForm<InvestorFormData>({
    resolver: zodResolver(investorSchema),
    defaultValues: {
      fullName: state.data.fullName || '',
      email: state.data.email || '',
      phoneNumber: state.data.phoneNumber || '',
      country: state.data.country || '',
      companyName: (state.data as InvestorRegistrationData)?.companyName || '',
      companyWebsite: (state.data as InvestorRegistrationData)?.companyWebsite || '',
      investorType: (state.data as InvestorRegistrationData)?.investorType || '',
      investmentSectors: (state.data as InvestorRegistrationData)?.investmentSectors || [],
      investmentRange: (state.data as InvestorRegistrationData)?.investmentRange || '',
      geographicFocus: (state.data as InvestorRegistrationData)?.geographicFocus || [],
      ikiaInterests: (state.data as InvestorRegistrationData)?.ikiaInterests || '',
      availableForMeetings: (state.data as InvestorRegistrationData)?.availableForMeetings || false,
    }
  })

  const watchedInvestmentSectors = watch('investmentSectors')
  const watchedGeographicFocus = watch('geographicFocus')

  const handleInvestmentSectorChange = (value: string, checked: boolean) => {
    const current = watchedInvestmentSectors || []
    if (checked) {
      setValue('investmentSectors', [...current, value])
    } else {
      setValue('investmentSectors', current.filter(item => item !== value))
    }
    trigger('investmentSectors')
  }

  const handleGeographicFocusChange = (value: string, checked: boolean) => {
    const current = watchedGeographicFocus || []
    if (checked) {
      setValue('geographicFocus', [...current, value])
    } else {
      setValue('geographicFocus', current.filter(item => item !== value))
    }
    trigger('geographicFocus')
  }

  const onSubmit = (data: InvestorFormData) => {
    const registrationData: Partial<InvestorRegistrationData> = {
      userType: 'investor',
      ...data,
    }
    updateData(registrationData)
    nextStep()
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          Investor Registration
        </h1>
        <p className="text-lg text-muted-foreground">
          Register to explore investment opportunities in Indigenous Knowledge Intellectual Assets
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Personal/Company Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5 text-primary-green" />
              <span>Personal & Company Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                leftIcon={<User className="w-4 h-4" />}
                error={errors.fullName?.message}
                required
                {...register('fullName')}
              />
              
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                required
                {...register('email')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                leftIcon={<Phone className="w-4 h-4" />}
                error={errors.phoneNumber?.message}
                required
                {...register('phoneNumber')}
              />
              
              <Select
                label="Country"
                placeholder="Select your country"
                options={COUNTRIES.map(country => ({
                  value: country.code,
                  label: `${country.flag} ${country.name}`
                }))}
                error={errors.country?.message}
                required
                {...register('country')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Company/Fund Name"
                placeholder="Your investment company or fund"
                leftIcon={<Building className="w-4 h-4" />}
                error={errors.companyName?.message}
                required
                {...register('companyName')}
              />
              
              <Input
                label="Company Website"
                type="url"
                placeholder="https://www.yourcompany.com"
                leftIcon={<Globe className="w-4 h-4" />}
                error={errors.companyWebsite?.message}
                helpText="Optional but recommended"
                {...register('companyWebsite')}
              />
            </div>
          </CardContent>
        </Card>

        {/* Investment Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-primary-green" />
              <span>Investment Profile</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Select
                label="Investor Type"
                placeholder="Select your investor type"
                options={INVESTOR_TYPES}
                error={errors.investorType?.message}
                required
                {...register('investorType')}
              />
              
              <Select
                label="Typical Investment Range"
                placeholder="Select investment range"
                options={INVESTMENT_RANGES}
                error={errors.investmentRange?.message}
                required
                {...register('investmentRange')}
              />
            </div>

            <div>
              <label className="text-sm font-medium leading-none mb-4 block">
                Primary Investment Sectors <span className="text-destructive">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {INTEREST_TRACKS.map((track) => (
                  <Checkbox
                    key={track.value}
                    label={track.label}
                    description={track.description}
                    checked={watchedInvestmentSectors?.includes(track.value) || false}
                    onChange={(e) => handleInvestmentSectorChange(track.value, e.target.checked)}
                  />
                ))}
              </div>
              {errors.investmentSectors && (
                <p className="text-sm text-destructive font-medium mt-2">
                  {errors.investmentSectors.message}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium leading-none mb-4 block">
                Geographic Investment Focus <span className="text-destructive">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {GEOGRAPHIC_FOCUS_OPTIONS.map((option) => (
                  <Checkbox
                    key={option.value}
                    label={option.label}
                    checked={watchedGeographicFocus?.includes(option.value) || false}
                    onChange={(e) => handleGeographicFocusChange(option.value, e.target.checked)}
                  />
                ))}
              </div>
              {errors.geographicFocus && (
                <p className="text-sm text-destructive font-medium mt-2">
                  {errors.geographicFocus.message}
                </p>
              )}
            </div>

            <Textarea
              label="Specific IKIA Types of Interest"
              placeholder="Describe specific types of Indigenous Knowledge Intellectual Assets you're interested in investing in..."
              rows={4}
              helpText="Optional: Help us understand your specific interests within IKIAs"
              {...register('ikiaInterests')}
            />
          </CardContent>
        </Card>

        {/* Matchmaking & Meetings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-primary-green" />
              <span>Matchmaking & Meetings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <Checkbox
              label="Available for 1-on-1 Meetings"
              description="I am interested in scheduling private meetings with entrepreneurs and innovators during the conference"
              {...register('availableForMeetings')}
            />

            <div className="space-y-2">
              <label className="text-sm font-medium leading-none">
                Investor Profile/Portfolio
              </label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  Upload your investor profile or portfolio
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  PDF up to 10MB (Optional)
                </p>
                <input
                  type="file"
                  accept=".pdf"
                  className="hidden"
                  id="investor-profile"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => document.getElementById('investor-profile')?.click()}
                >
                  Choose File
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                This helps entrepreneurs understand your investment criteria and portfolio
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button
            type="submit"
            size="lg"
            loading={isSubmitting}
            className="min-w-[200px]"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
