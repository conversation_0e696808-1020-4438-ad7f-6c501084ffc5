'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Checkbox } from '@/components/ui/Checkbox'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { COUNTRIES, INDUSTRIES, BOOTH_SIZES, POWER_REQUIREMENTS } from '@/lib/constants'
import { ExhibitorRegistrationData } from '@/types/registration'
import { User, Mail, Phone, Globe, Building, FileText, Upload, Zap, Users } from 'lucide-react'

const exhibitorSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),
  country: z.string().min(1, 'Please select your country'),
  jobTitle: z.string().min(2, 'Job title is required'),
  companyName: z.string().min(2, 'Company name is required'),
  companyWebsite: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  companyDescription: z.string().min(50, 'Company description must be at least 50 characters').max(1000, 'Company description must be less than 1000 characters'),
  primaryIndustry: z.string().min(1, 'Please select your primary industry'),
  productsServices: z.string().min(50, 'Please describe your products/services (minimum 50 characters)'),
  boothSize: z.enum(['standard', 'premium', 'custom'], { required_error: 'Please select a booth size' }),
  powerRequirements: z.array(z.string()).default([]),
  staffCount: z.number().min(1, 'At least 1 staff member is required').max(20, 'Maximum 20 staff members allowed'),
})

type ExhibitorFormData = z.infer<typeof exhibitorSchema>

export function ExhibitorRegistrationForm() {
  const { state, updateData, nextStep } = useRegistration()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    trigger
  } = useForm<ExhibitorFormData>({
    resolver: zodResolver(exhibitorSchema),
    defaultValues: {
      fullName: state.data.fullName || '',
      email: state.data.email || '',
      phoneNumber: state.data.phoneNumber || '',
      country: state.data.country || '',
      jobTitle: (state.data as ExhibitorRegistrationData)?.jobTitle || '',
      companyName: (state.data as ExhibitorRegistrationData)?.companyName || '',
      companyWebsite: (state.data as ExhibitorRegistrationData)?.companyWebsite || '',
      companyDescription: (state.data as ExhibitorRegistrationData)?.companyDescription || '',
      primaryIndustry: (state.data as ExhibitorRegistrationData)?.primaryIndustry || '',
      productsServices: (state.data as ExhibitorRegistrationData)?.productsServices || '',
      boothSize: (state.data as ExhibitorRegistrationData)?.boothSize || 'standard',
      powerRequirements: (state.data as ExhibitorRegistrationData)?.powerRequirements || [],
      staffCount: (state.data as ExhibitorRegistrationData)?.staffCount || 1,
    }
  })

  const watchedPowerRequirements = watch('powerRequirements')

  const handlePowerRequirementChange = (value: string, checked: boolean) => {
    const current = watchedPowerRequirements || []
    if (checked) {
      setValue('powerRequirements', [...current, value])
    } else {
      setValue('powerRequirements', current.filter(item => item !== value))
    }
    trigger('powerRequirements')
  }

  const onSubmit = (data: ExhibitorFormData) => {
    const registrationData: Partial<ExhibitorRegistrationData> = {
      userType: 'exhibitor',
      ...data,
    }
    updateData(registrationData)
    nextStep()
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          Exhibitor Registration
        </h1>
        <p className="text-lg text-muted-foreground">
          Register to showcase your products and services at IKIA 2025
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Contact Person Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5 text-primary-green" />
              <span>Contact Person Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                leftIcon={<User className="w-4 h-4" />}
                error={errors.fullName?.message}
                required
                {...register('fullName')}
              />
              
              <Input
                label="Job Title"
                placeholder="Your position in the company"
                error={errors.jobTitle?.message}
                required
                {...register('jobTitle')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                required
                {...register('email')}
              />
              
              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                leftIcon={<Phone className="w-4 h-4" />}
                error={errors.phoneNumber?.message}
                required
                {...register('phoneNumber')}
              />
            </div>

            <Select
              label="Country"
              placeholder="Select your country"
              options={COUNTRIES.map(country => ({
                value: country.code,
                label: `${country.flag} ${country.name}`
              }))}
              error={errors.country?.message}
              required
              {...register('country')}
            />
          </CardContent>
        </Card>

        {/* Company Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="w-5 h-5 text-primary-green" />
              <span>Company Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Company Name"
                placeholder="Your company name"
                leftIcon={<Building className="w-4 h-4" />}
                error={errors.companyName?.message}
                required
                {...register('companyName')}
              />
              
              <Input
                label="Company Website"
                type="url"
                placeholder="https://www.yourcompany.com"
                leftIcon={<Globe className="w-4 h-4" />}
                error={errors.companyWebsite?.message}
                helpText="Optional but recommended"
                {...register('companyWebsite')}
              />
            </div>

            <Textarea
              label="Company Description"
              placeholder="Describe your company, its mission, and what makes it unique..."
              rows={4}
              maxLength={1000}
              error={errors.companyDescription?.message}
              helpText="This will be used in promotional materials (50-1000 characters)"
              required
              {...register('companyDescription')}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Select
                label="Primary Industry"
                placeholder="Select your industry"
                options={INDUSTRIES.map(industry => ({
                  value: industry.value,
                  label: industry.label
                }))}
                error={errors.primaryIndustry?.message}
                required
                {...register('primaryIndustry')}
              />
            </div>

            <Textarea
              label="Products/Services to Exhibit"
              placeholder="Describe the specific products or services you plan to showcase at the exhibition..."
              rows={4}
              error={errors.productsServices?.message}
              helpText="Be specific about what visitors can expect to see at your booth"
              required
              {...register('productsServices')}
            />
          </CardContent>
        </Card>

        {/* Booth Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-primary-green" />
              <span>Booth Requirements</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label className="text-sm font-medium leading-none mb-4 block">
                Preferred Booth Size <span className="text-destructive">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {BOOTH_SIZES.map((size) => (
                  <div key={size.value} className="space-y-2">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        value={size.value}
                        {...register('boothSize')}
                        className="sr-only"
                      />
                      <div className="w-4 h-4 border-2 border-border rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-primary-green rounded-full opacity-0 peer-checked:opacity-100" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">{size.label}</div>
                        <div className="text-xs text-muted-foreground">{size.description}</div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
              {errors.boothSize && (
                <p className="text-sm text-destructive font-medium mt-2">
                  {errors.boothSize.message}
                </p>
              )}
            </div>

            <div>
              <label className="text-sm font-medium leading-none mb-4 block flex items-center space-x-2">
                <Zap className="w-4 h-4" />
                <span>Power Requirements</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {POWER_REQUIREMENTS.map((requirement) => (
                  <Checkbox
                    key={requirement.value}
                    label={requirement.label}
                    checked={watchedPowerRequirements?.includes(requirement.value) || false}
                    onChange={(e) => handlePowerRequirementChange(requirement.value, e.target.checked)}
                  />
                ))}
              </div>
            </div>

            <Input
              label="Number of Staff Attending"
              type="number"
              min="1"
              max="20"
              placeholder="1"
              leftIcon={<Users className="w-4 h-4" />}
              error={errors.staffCount?.message}
              helpText="How many staff members will be working at your booth?"
              required
              {...register('staffCount', { valueAsNumber: true })}
            />
          </CardContent>
        </Card>

        {/* File Uploads */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="w-5 h-5 text-primary-green" />
              <span>Company Materials</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium leading-none">
                  Company Logo
                </label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Upload your company logo
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    PNG, JPG up to 5MB
                  </p>
                  <input
                    type="file"
                    accept="image/*"
                    className="hidden"
                    id="company-logo"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => document.getElementById('company-logo')?.click()}
                  >
                    Choose File
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium leading-none">
                  Company Profile/Brochure
                </label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <FileText className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Upload company brochure
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    PDF up to 10MB
                  </p>
                  <input
                    type="file"
                    accept=".pdf"
                    className="hidden"
                    id="company-brochure"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => document.getElementById('company-brochure')?.click()}
                  >
                    Choose File
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button
            type="submit"
            size="lg"
            loading={isSubmitting}
            className="min-w-[200px]"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
