'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Checkbox } from '@/components/ui/Checkbox'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { COUNTRIES, DIETARY_RESTRICTIONS } from '@/lib/constants'
import { VIPRegistrationData } from '@/types/registration'
import { 
  Crown, 
  User, 
  Mail, 
  Phone, 
  Globe, 
  Building, 
  Briefcase, 
  Key, 
  Utensils, 
  Accessibility,
  Shield,
  Star
} from 'lucide-react'

const vipSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),
  country: z.string().min(1, 'Please select your country'),
  organization: z.string().min(2, 'Organization/Affiliation is required'),
  jobTitle: z.string().min(2, 'Job title/role is required'),
  invitationCode: z.string().optional(),
  dietaryRestrictions: z.array(z.string()).default([]),
  accessibilityNeeds: z.string().optional(),
})

type VIPFormData = z.infer<typeof vipSchema>

const VIP_CATEGORIES = [
  { value: 'keynote-speaker', label: 'Keynote Speaker', icon: '🎤' },
  { value: 'government-official', label: 'Government Official', icon: '🏛️' },
  { value: 'industry-leader', label: 'Industry Leader', icon: '👔' },
  { value: 'academic-researcher', label: 'Academic/Researcher', icon: '🎓' },
  { value: 'cultural-leader', label: 'Cultural Leader', icon: '🌟' },
  { value: 'media-representative', label: 'Media Representative', icon: '📺' },
  { value: 'international-delegate', label: 'International Delegate', icon: '🌍' },
  { value: 'special-guest', label: 'Special Guest', icon: '⭐' },
]

export function VIPRegistrationForm() {
  const { state, updateData, nextStep } = useRegistration()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    trigger
  } = useForm<VIPFormData>({
    resolver: zodResolver(vipSchema),
    defaultValues: {
      fullName: state.data.fullName || '',
      email: state.data.email || '',
      phoneNumber: state.data.phoneNumber || '',
      country: state.data.country || '',
      organization: (state.data as VIPRegistrationData)?.organization || '',
      jobTitle: (state.data as VIPRegistrationData)?.jobTitle || '',
      invitationCode: (state.data as VIPRegistrationData)?.invitationCode || '',
      dietaryRestrictions: (state.data as VIPRegistrationData)?.dietaryRestrictions || [],
      accessibilityNeeds: (state.data as VIPRegistrationData)?.accessibilityNeeds || '',
    }
  })

  const watchedDietaryRestrictions = watch('dietaryRestrictions')

  const handleDietaryRestrictionChange = (value: string, checked: boolean) => {
    const current = watchedDietaryRestrictions || []
    if (checked) {
      setValue('dietaryRestrictions', [...current, value])
    } else {
      setValue('dietaryRestrictions', current.filter(item => item !== value))
    }
    trigger('dietaryRestrictions')
  }

  const onSubmit = (data: VIPFormData) => {
    const registrationData: Partial<VIPRegistrationData> = {
      userType: 'vip',
      ...data,
    }
    updateData(registrationData)
    nextStep()
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Elegant Header with VIP Branding */}
      <div className="text-center space-y-6">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-yellow to-secondary-sienna rounded-full shadow-medium">
          <Crown className="w-10 h-10 text-white" />
        </div>
        
        <div className="space-y-3">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary-brown via-primary-green to-primary-yellow bg-clip-text text-transparent">
            VIP Registration
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Welcome to our exclusive VIP registration. As a distinguished guest, you'll receive 
            premium access and personalized attention throughout IKIA 2025.
          </p>
        </div>

        {/* VIP Benefits Preview */}
        <div className="bg-gradient-to-r from-primary-green/5 via-primary-yellow/5 to-secondary-sienna/5 rounded-2xl p-6 border border-primary-green/20">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Star className="w-5 h-5 text-primary-yellow" />
            <span className="text-sm font-semibold text-primary-brown">VIP EXCLUSIVE BENEFITS</span>
            <Star className="w-5 h-5 text-primary-yellow" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-primary-green" />
              <span>VIP Lounge Access</span>
            </div>
            <div className="flex items-center space-x-2">
              <Crown className="w-4 h-4 text-primary-yellow" />
              <span>Priority Seating</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-secondary-sienna" />
              <span>Exclusive Networking</span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-primary-brown" />
              <span>Premium Amenities</span>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Personal Information - Enhanced Design */}
        <Card className="border-l-4 border-l-primary-green shadow-soft hover:shadow-medium transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-primary-green/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-green rounded-lg flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Personal Information</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Your distinguished profile details
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Enter your distinguished name"
                leftIcon={<User className="w-4 h-4" />}
                error={errors.fullName?.message}
                required
                className="transition-all duration-200 focus:ring-2 focus:ring-primary-green/20"
                {...register('fullName')}
              />
              
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                required
                className="transition-all duration-200 focus:ring-2 focus:ring-primary-green/20"
                {...register('email')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                leftIcon={<Phone className="w-4 h-4" />}
                error={errors.phoneNumber?.message}
                required
                className="transition-all duration-200 focus:ring-2 focus:ring-primary-green/20"
                {...register('phoneNumber')}
              />
              
              <Select
                label="Country"
                placeholder="Select your country"
                options={COUNTRIES.map(country => ({
                  value: country.code,
                  label: `${country.flag} ${country.name}`
                }))}
                error={errors.country?.message}
                required
                {...register('country')}
              />
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card className="border-l-4 border-l-primary-yellow shadow-soft hover:shadow-medium transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-primary-yellow/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-yellow rounded-lg flex items-center justify-center">
                <Building className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Professional Details</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Your organizational affiliation and role
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Organization/Affiliation"
                placeholder="Your organization or institution"
                leftIcon={<Building className="w-4 h-4" />}
                error={errors.organization?.message}
                required
                helpText="Government, company, university, or cultural organization"
                className="transition-all duration-200 focus:ring-2 focus:ring-primary-yellow/20"
                {...register('organization')}
              />
              
              <Input
                label="Job Title/Role"
                placeholder="Your position or title"
                leftIcon={<Briefcase className="w-4 h-4" />}
                error={errors.jobTitle?.message}
                required
                helpText="e.g., Minister, CEO, Professor, Cultural Leader"
                className="transition-all duration-200 focus:ring-2 focus:ring-primary-yellow/20"
                {...register('jobTitle')}
              />
            </div>

            {/* VIP Category Selection */}
            <div className="space-y-4">
              <label className="text-sm font-medium leading-none">
                VIP Category <span className="text-muted-foreground">(Optional)</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {VIP_CATEGORIES.map((category) => (
                  <div
                    key={category.value}
                    className="group relative p-3 border border-border rounded-lg hover:border-primary-green/50 hover:bg-primary-green/5 transition-all duration-200 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{category.icon}</span>
                      <span className="text-xs font-medium text-foreground group-hover:text-primary-green transition-colors">
                        {category.label}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Invitation & Access */}
        <Card className="border-l-4 border-l-secondary-sienna shadow-soft hover:shadow-medium transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-secondary-sienna/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-secondary-sienna rounded-lg flex items-center justify-center">
                <Key className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Invitation & Access</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Special access credentials and requirements
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <Input
              label="Invitation Code"
              placeholder="Enter your VIP invitation code (if applicable)"
              leftIcon={<Key className="w-4 h-4" />}
              helpText="Leave blank if you don't have an invitation code"
              className="transition-all duration-200 focus:ring-2 focus:ring-secondary-sienna/20"
              {...register('invitationCode')}
            />
          </CardContent>
        </Card>

        {/* Hospitality Preferences */}
        <Card className="border-l-4 border-l-secondary-blue shadow-soft hover:shadow-medium transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-secondary-blue/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-secondary-blue rounded-lg flex items-center justify-center">
                <Utensils className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Hospitality Preferences</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Ensuring your comfort and dietary requirements
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div>
              <label className="text-sm font-medium leading-none mb-4 block">
                Dietary Restrictions & Preferences
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {DIETARY_RESTRICTIONS.map((restriction) => (
                  <Checkbox
                    key={restriction.value}
                    label={restriction.label}
                    checked={watchedDietaryRestrictions?.includes(restriction.value) || false}
                    onChange={(e) => handleDietaryRestrictionChange(restriction.value, e.target.checked)}
                    className="transition-all duration-200"
                  />
                ))}
              </div>
            </div>

            <Textarea
              label="Special Accessibility Requirements"
              placeholder="Please describe any special accommodations you may need (wheelchair access, sign language interpretation, security requirements, etc.)"
              rows={4}
              maxLength={500}
              helpText="We are committed to providing exceptional service for all our VIP guests"
              className="transition-all duration-200 focus:ring-2 focus:ring-secondary-blue/20"
              {...register('accessibilityNeeds')}
            />
          </CardContent>
        </Card>

        {/* Submit Button - Premium Design */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button
            type="submit"
            size="lg"
            loading={isSubmitting}
            className="min-w-[250px] bg-gradient-to-r from-primary-green to-primary-yellow hover:from-primary-green/90 hover:to-primary-yellow/90 text-white font-semibold shadow-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105"
          >
            <Crown className="w-5 h-5 mr-2" />
            Continue VIP Registration
          </Button>
        </div>
      </form>
    </div>
  )
}
