'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { USER_TYPE_OPTIONS } from '@/lib/constants'
import { UserType } from '@/types/registration'
import { cn } from '@/lib/utils'
import { Check } from 'lucide-react'

export function UserTypeSelection() {
  const { state, updateData, nextStep } = useRegistration()
  const [selectedType, setSelectedType] = React.useState<UserType | null>(
    state.data.userType || null
  )

  const handleTypeSelect = (type: UserType) => {
    setSelectedType(type)
    updateData({ userType: type })
  }

  const handleContinue = () => {
    if (selectedType) {
      nextStep()
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          Select Your Registration Type
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Choose the registration type that best describes your participation in the 
          1st International Investment Conference and Trade Fair on Indigenous Knowledge 
          Intellectual Assets (IKIAs) 2025.
        </p>
      </div>

      {/* User Type Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {USER_TYPE_OPTIONS.map((option) => (
          <Card
            key={option.type}
            className={cn(
              'cursor-pointer transition-all duration-200 hover:shadow-medium',
              'border-2 relative overflow-hidden',
              selectedType === option.type
                ? 'border-primary-green bg-primary-green/5'
                : 'border-border hover:border-primary-green/50'
            )}
            onClick={() => handleTypeSelect(option.type)}
          >
            {/* Selection Indicator */}
            {selectedType === option.type && (
              <div className="absolute top-4 right-4 w-6 h-6 bg-primary-green rounded-full flex items-center justify-center">
                <Check className="w-4 h-4 text-white" strokeWidth={2.5} />
              </div>
            )}

            <CardContent className="p-6 space-y-4">
              {/* Icon and Title */}
              <div className="space-y-3">
                <div className="text-4xl">{option.icon}</div>
                <div>
                  <h3 className="text-xl font-semibold text-foreground">
                    {option.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {option.description}
                  </p>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-foreground">
                  What's included:
                </h4>
                <ul className="space-y-1">
                  {option.features.slice(0, 3).map((feature, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start">
                      <span className="w-1 h-1 bg-primary-green rounded-full mt-2 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                  {option.features.length > 3 && (
                    <li className="text-xs text-muted-foreground">
                      + {option.features.length - 3} more benefits
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Continue Button */}
      <div className="flex justify-center pt-6">
        <Button
          size="lg"
          onClick={handleContinue}
          disabled={!selectedType}
          className="min-w-[200px]"
        >
          Continue to Registration
        </Button>
      </div>

      {/* Additional Information */}
      <div className="bg-muted/50 rounded-lg p-6 text-center">
        <h3 className="text-lg font-semibold text-foreground mb-2">
          Need Help Choosing?
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          If you're unsure which registration type is right for you, please contact our support team.
        </p>
        <div className="flex flex-col sm:flex-row gap-2 justify-center">
          <Button variant="outline" size="sm">
            Contact Support
          </Button>
          <Button variant="ghost" size="sm">
            View Detailed Comparison
          </Button>
        </div>
      </div>
    </div>
  )
}
