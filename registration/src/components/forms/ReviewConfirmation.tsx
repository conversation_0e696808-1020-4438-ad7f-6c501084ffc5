'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTit<PERSON> } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Checkbox } from '@/components/ui/Checkbox'
import { useRegistration } from '@/context/RegistrationContext'
import { USER_TYPE_OPTIONS, COUNTRIES, INDUSTRIES, DIETARY_RESTRICTIONS, INTEREST_TRACKS } from '@/lib/constants'
import { formatCurrency } from '@/lib/utils'
import { Edit, Check, User, Building, Target, Utensils, DollarSign } from 'lucide-react'

export function ReviewConfirmation() {
  const { state, goToStep, submitRegistration } = useRegistration()
  const [agreedToTerms, setAgreedToTerms] = React.useState(false)

  const userTypeOption = USER_TYPE_OPTIONS.find(option => option.type === state.data.userType)
  const country = COUNTRIES.find(c => c.code === state.data.country)

  const handleEdit = () => {
    goToStep('details')
  }

  const handleSubmit = async () => {
    if (agreedToTerms) {
      await submitRegistration()
    }
  }

  const renderPersonalInfo = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <User className="w-5 h-5 text-primary-green" />
            <span>Personal Information</span>
          </div>
          <Button variant="ghost" size="sm" onClick={handleEdit}>
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Full Name</label>
            <p className="text-foreground">{state.data.fullName}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Email</label>
            <p className="text-foreground">{state.data.email}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
            <p className="text-foreground">{state.data.phoneNumber}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Country</label>
            <p className="text-foreground">
              {country ? `${country.flag} ${country.name}` : state.data.country}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderRegistrationType = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Target className="w-5 h-5 text-primary-green" />
          <span>Registration Type</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4 p-4 bg-primary-green/5 rounded-lg border border-primary-green/20">
          <div className="text-2xl">{userTypeOption?.icon}</div>
          <div>
            <h3 className="font-semibold text-foreground">{userTypeOption?.title}</h3>
            <p className="text-sm text-muted-foreground">{userTypeOption?.description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderGuestSpecific = () => {
    if (state.data.userType !== 'guest') return null
    
    const guestData = state.data as any
    
    return (
      <>
        {guestData.organization && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="w-5 h-5 text-primary-green" />
                <span>Organization</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-foreground">{guestData.organization}</p>
            </CardContent>
          </Card>
        )}
        
        {guestData.dietaryRestrictions?.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Utensils className="w-5 h-5 text-primary-green" />
                <span>Dietary Restrictions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {guestData.dietaryRestrictions.map((restriction: string) => {
                  const restrictionOption = DIETARY_RESTRICTIONS.find(r => r.value === restriction)
                  return (
                    <span key={restriction} className="px-3 py-1 bg-muted rounded-full text-sm">
                      {restrictionOption?.label || restriction}
                    </span>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}
        
        {guestData.interestTracks?.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-primary-green" />
                <span>Interest Tracks</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {guestData.interestTracks.map((track: string) => {
                  const trackOption = INTEREST_TRACKS.find(t => t.value === track)
                  return (
                    <div key={track} className="flex items-start space-x-2">
                      <Check className="w-4 h-4 text-primary-green mt-0.5" />
                      <div>
                        <p className="font-medium text-sm">{trackOption?.label || track}</p>
                        {trackOption?.description && (
                          <p className="text-xs text-muted-foreground">{trackOption.description}</p>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </>
    )
  }

  const renderExhibitorSpecific = () => {
    if (state.data.userType !== 'exhibitor') return null
    
    const exhibitorData = state.data as any
    
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="w-5 h-5 text-primary-green" />
              <span>Company Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                <p className="text-foreground">{exhibitorData.companyName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Job Title</label>
                <p className="text-foreground">{exhibitorData.jobTitle}</p>
              </div>
              {exhibitorData.companyWebsite && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Website</label>
                  <p className="text-foreground">{exhibitorData.companyWebsite}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-muted-foreground">Booth Size</label>
                <p className="text-foreground capitalize">{exhibitorData.boothSize}</p>
              </div>
            </div>
            {exhibitorData.companyDescription && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Company Description</label>
                <p className="text-foreground text-sm">{exhibitorData.companyDescription}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </>
    )
  }

  const renderInvestorSpecific = () => {
    if (state.data.userType !== 'investor') return null
    
    const investorData = state.data as any
    
    return (
      <>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5 text-primary-green" />
              <span>Investment Profile</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Company/Fund</label>
                <p className="text-foreground">{investorData.companyName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Investor Type</label>
                <p className="text-foreground">{investorData.investorType}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Investment Range</label>
                <p className="text-foreground">{investorData.investmentRange}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Available for Meetings</label>
                <p className="text-foreground">{investorData.availableForMeetings ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          Review Your Registration
        </h1>
        <p className="text-lg text-muted-foreground">
          Please review your information before submitting your registration
        </p>
      </div>

      {/* Registration Details */}
      <div className="space-y-6">
        {renderRegistrationType()}
        {renderPersonalInfo()}
        {renderGuestSpecific()}
        {renderExhibitorSpecific()}
        {renderInvestorSpecific()}
      </div>

      {/* Terms and Conditions */}
      <Card>
        <CardContent className="pt-6">
          <Checkbox
            label="I agree to the Terms & Conditions and Privacy Policy"
            description="By checking this box, you agree to our terms of service and privacy policy"
            checked={agreedToTerms}
            onChange={(e) => setAgreedToTerms(e.target.checked)}
            required
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-between items-center">
        <Button variant="outline" onClick={handleEdit}>
          <Edit className="w-4 h-4 mr-2" />
          Edit Information
        </Button>
        
        <Button
          size="lg"
          onClick={handleSubmit}
          disabled={!agreedToTerms}
          loading={state.isSubmitting}
          className="min-w-[200px]"
        >
          Submit Registration
        </Button>
      </div>
    </div>
  )
}
