'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Checkbox } from '@/components/ui/Checkbox'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { COUNTRIES, DIETARY_RESTRICTIONS, INTEREST_TRACKS } from '@/lib/constants'
import { GuestRegistrationData } from '@/types/registration'
import { User, Mail, Phone, Globe, Building, Utensils, Accessibility, Target } from 'lucide-react'

const guestSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),
  country: z.string().min(1, 'Please select your country'),
  organization: z.string().optional(),
  dietaryRestrictions: z.array(z.string()).default([]),
  accessibilityNeeds: z.string().optional(),
  interestTracks: z.array(z.string()).min(1, 'Please select at least one interest track'),
})

type GuestFormData = z.infer<typeof guestSchema>

export function GuestRegistrationForm() {
  const { state, updateData, nextStep } = useRegistration()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    trigger
  } = useForm<GuestFormData>({
    resolver: zodResolver(guestSchema),
    defaultValues: {
      fullName: state.data.fullName || '',
      email: state.data.email || '',
      phoneNumber: state.data.phoneNumber || '',
      country: state.data.country || '',
      organization: (state.data as GuestRegistrationData)?.organization || '',
      dietaryRestrictions: (state.data as GuestRegistrationData)?.dietaryRestrictions || [],
      accessibilityNeeds: (state.data as GuestRegistrationData)?.accessibilityNeeds || '',
      interestTracks: (state.data as GuestRegistrationData)?.interestTracks || [],
    }
  })

  const watchedDietaryRestrictions = watch('dietaryRestrictions')
  const watchedInterestTracks = watch('interestTracks')

  const handleDietaryRestrictionChange = (value: string, checked: boolean) => {
    const current = watchedDietaryRestrictions || []
    if (checked) {
      setValue('dietaryRestrictions', [...current, value])
    } else {
      setValue('dietaryRestrictions', current.filter(item => item !== value))
    }
    trigger('dietaryRestrictions')
  }

  const handleInterestTrackChange = (value: string, checked: boolean) => {
    const current = watchedInterestTracks || []
    if (checked) {
      setValue('interestTracks', [...current, value])
    } else {
      setValue('interestTracks', current.filter(item => item !== value))
    }
    trigger('interestTracks')
  }

  const onSubmit = (data: GuestFormData) => {
    const registrationData: Partial<GuestRegistrationData> = {
      userType: 'guest',
      ...data,
    }
    updateData(registrationData)
    nextStep()
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          Guest/Attendee Registration
        </h1>
        <p className="text-lg text-muted-foreground">
          Complete your registration to attend the IKIA 2025 conference
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="w-5 h-5 text-primary-green" />
              <span>Personal Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                leftIcon={<User className="w-4 h-4" />}
                error={errors.fullName?.message}
                required
                {...register('fullName')}
              />
              
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                required
                {...register('email')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                leftIcon={<Phone className="w-4 h-4" />}
                error={errors.phoneNumber?.message}
                required
                {...register('phoneNumber')}
              />
              
              <Select
                label="Country of Residence"
                placeholder="Select your country"
                options={COUNTRIES.map(country => ({
                  value: country.code,
                  label: `${country.flag} ${country.name}`
                }))}
                error={errors.country?.message}
                required
                {...register('country')}
              />
            </div>

            <Input
              label="Organization/Affiliation"
              placeholder="Your company, university, or organization (optional)"
              leftIcon={<Building className="w-4 h-4" />}
              helpText="This field is optional but helps us understand our attendees better"
              {...register('organization')}
            />
          </CardContent>
        </Card>

        {/* Dietary Restrictions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Utensils className="w-5 h-5 text-primary-green" />
              <span>Dietary Restrictions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {DIETARY_RESTRICTIONS.map((restriction) => (
                <Checkbox
                  key={restriction.value}
                  label={restriction.label}
                  checked={watchedDietaryRestrictions?.includes(restriction.value) || false}
                  onChange={(e) => handleDietaryRestrictionChange(restriction.value, e.target.checked)}
                />
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Accessibility Needs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Accessibility className="w-5 h-5 text-primary-green" />
              <span>Accessibility Needs</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              label="Special Accessibility Requirements"
              placeholder="Please describe any accessibility accommodations you may need (wheelchair access, sign language interpretation, etc.)"
              rows={4}
              maxLength={500}
              helpText="We are committed to making our event accessible to all attendees"
              {...register('accessibilityNeeds')}
            />
          </CardContent>
        </Card>

        {/* Interest Tracks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-primary-green" />
              <span>Conference Interest Tracks</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Select the conference tracks that interest you most. This helps us personalize your experience.
            </p>
            
            <div className="space-y-3">
              {INTEREST_TRACKS.map((track) => (
                <Checkbox
                  key={track.value}
                  label={track.label}
                  description={track.description}
                  checked={watchedInterestTracks?.includes(track.value) || false}
                  onChange={(e) => handleInterestTrackChange(track.value, e.target.checked)}
                />
              ))}
            </div>
            
            {errors.interestTracks && (
              <p className="text-sm text-destructive font-medium">
                {errors.interestTracks.message}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button
            type="submit"
            size="lg"
            loading={isSubmitting}
            className="min-w-[200px]"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
