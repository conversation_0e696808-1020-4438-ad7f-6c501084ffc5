'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { USER_TYPE_OPTIONS } from '@/lib/constants'
import { 
  CheckCircle, 
  Mail, 
  Calendar, 
  Download, 
  Share2, 
  Home, 
  Phone,
  ExternalLink
} from 'lucide-react'

export function RegistrationSuccess() {
  const { state, resetRegistration } = useRegistration()
  
  const userTypeOption = USER_TYPE_OPTIONS.find(option => option.type === state.data.userType)
  const registrationId = `IKIA2025-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

  const handleNewRegistration = () => {
    resetRegistration()
  }

  const handleDownloadConfirmation = () => {
    // In a real app, this would generate and download a PDF confirmation
    console.log('Downloading confirmation...')
  }

  const handleShareRegistration = () => {
    if (navigator.share) {
      navigator.share({
        title: 'IKIA 2025 Registration Confirmed',
        text: `I've registered for the 1st International Investment Conference on Indigenous Knowledge Intellectual Assets!`,
        url: window.location.origin
      })
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(window.location.origin)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Success Header */}
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <div className="w-20 h-20 bg-success rounded-full flex items-center justify-center">
            <CheckCircle className="w-12 h-12 text-white" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h1 className="text-4xl font-bold text-success">
            Registration Successful!
          </h1>
          <p className="text-xl text-muted-foreground">
            Thank you for registering for IKIA 2025
          </p>
        </div>
      </div>

      {/* Registration Confirmation */}
      <Card className="border-success/20 bg-success/5">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-success">
            <CheckCircle className="w-5 h-5" />
            <span>Registration Confirmed</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Registration ID</label>
              <p className="text-lg font-mono text-foreground">{registrationId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Registration Type</label>
              <p className="text-lg text-foreground">
                {userTypeOption?.icon} {userTypeOption?.title}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Registered Name</label>
              <p className="text-lg text-foreground">{state.data.fullName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Email Address</label>
              <p className="text-lg text-foreground">{state.data.email}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-primary-green" />
            <span>What Happens Next?</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold">
                1
              </div>
              <div>
                <h4 className="font-semibold text-foreground">Confirmation Email</h4>
                <p className="text-sm text-muted-foreground">
                  A confirmation email has been sent to <strong>{state.data.email}</strong>. 
                  Please check your spam folder if you don't see it within 10 minutes.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold">
                2
              </div>
              <div>
                <h4 className="font-semibold text-foreground">Event Details</h4>
                <p className="text-sm text-muted-foreground">
                  You'll receive detailed event information including venue, schedule, 
                  and access instructions closer to the conference date.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-primary-green text-white rounded-full flex items-center justify-center text-sm font-bold">
                3
              </div>
              <div>
                <h4 className="font-semibold text-foreground">Attendee Dashboard</h4>
                <p className="text-sm text-muted-foreground">
                  Access your personalized attendee dashboard to view your schedule, 
                  connect with other participants, and access conference materials.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Button 
          variant="outline" 
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={handleDownloadConfirmation}
        >
          <Download className="w-6 h-6" />
          <span className="text-sm">Download Confirmation</span>
        </Button>
        
        <Button 
          variant="outline" 
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={handleShareRegistration}
        >
          <Share2 className="w-6 h-6" />
          <span className="text-sm">Share Registration</span>
        </Button>
        
        <Button 
          variant="outline" 
          className="h-auto p-4 flex flex-col items-center space-y-2"
        >
          <Calendar className="w-6 h-6" />
          <span className="text-sm">Add to Calendar</span>
        </Button>
        
        <Button 
          variant="outline" 
          className="h-auto p-4 flex flex-col items-center space-y-2"
        >
          <ExternalLink className="w-6 h-6" />
          <span className="text-sm">View Agenda</span>
        </Button>
      </div>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Phone className="w-5 h-5 text-primary-green" />
            <span>Need Help?</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            If you have any questions about your registration or the conference, 
            please don't hesitate to contact our support team.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-3">
              <Mail className="w-5 h-5 text-primary-green" />
              <div>
                <p className="font-medium text-foreground">Email Support</p>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Phone className="w-5 h-5 text-primary-green" />
              <div>
                <p className="font-medium text-foreground">Phone Support</p>
                <p className="text-sm text-muted-foreground">+254 (0) 700 123 456</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button 
          size="lg"
          onClick={() => window.location.href = '/'}
          className="flex items-center space-x-2"
        >
          <Home className="w-4 h-4" />
          <span>Return to Home</span>
        </Button>
        
        <Button 
          variant="outline" 
          size="lg"
          onClick={handleNewRegistration}
          className="flex items-center space-x-2"
        >
          <span>Register Another Person</span>
        </Button>
      </div>

      {/* Social Sharing */}
      <div className="text-center space-y-4 pt-8 border-t border-border">
        <h3 className="text-lg font-semibold text-foreground">
          Spread the Word!
        </h3>
        <p className="text-muted-foreground">
          Help us make IKIA 2025 a success by sharing with your network
        </p>
        <div className="flex justify-center space-x-4">
          <Button variant="outline" size="sm">Share on LinkedIn</Button>
          <Button variant="outline" size="sm">Share on Twitter</Button>
          <Button variant="outline" size="sm">Share on Facebook</Button>
        </div>
      </div>
    </div>
  )
}
