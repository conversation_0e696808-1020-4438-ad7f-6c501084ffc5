'use client'

import React from 'react'
import { useRegistration } from '@/context/RegistrationContext'
import { Progress } from '@/components/ui/Progress'
import { Button } from '@/components/ui/Button'
import { ArrowLeft, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface RegistrationLayoutProps {
  children: React.ReactNode
  showBackButton?: boolean
  showProgress?: boolean
}

export function RegistrationLayout({ 
  children, 
  showBackButton = true, 
  showProgress = true 
}: RegistrationLayoutProps) {
  const { state, prevStep } = useRegistration()

  const getProgressSteps = () => {
    const baseSteps = [
      {
        id: 'user-type',
        title: 'Select Type',
        description: 'Choose registration type',
        status: state.currentStep === 'user-type' ? 'current' : 
                state.currentStep === 'details' || state.currentStep === 'review' || state.currentStep === 'success' ? 'completed' : 'upcoming'
      },
      {
        id: 'details',
        title: 'Your Details',
        description: 'Personal information',
        status: state.currentStep === 'details' ? 'current' : 
                state.currentStep === 'review' || state.currentStep === 'success' ? 'completed' : 'upcoming'
      },
      {
        id: 'review',
        title: 'Review',
        description: 'Confirm information',
        status: state.currentStep === 'review' ? 'current' : 
                state.currentStep === 'success' ? 'completed' : 'upcoming'
      },
      {
        id: 'success',
        title: 'Complete',
        description: 'Registration complete',
        status: state.currentStep === 'success' ? 'current' : 'upcoming'
      }
    ]

    return baseSteps.map(step => ({
      ...step,
      status: step.status as 'completed' | 'current' | 'upcoming'
    }))
  }

  const canGoBack = state.currentStep !== 'user-type' && state.currentStep !== 'success'

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white border-b border-border sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-green rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">IKIA</span>
                </div>
                <div>
                  <h1 className="text-lg font-semibold text-foreground">
                    Conference Registration
                  </h1>
                  <p className="text-xs text-muted-foreground">
                    IKIA 2025 - Indigenous Knowledge Intellectual Assets
                  </p>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center space-x-4">
              {showBackButton && canGoBack && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={prevStep}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back</span>
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Home className="w-4 h-4" />
                <span className="hidden sm:inline">Home</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Indicator */}
      {showProgress && state.currentStep !== 'success' && (
        <div className="bg-muted/30 border-b border-border">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <Progress steps={getProgressSteps()} />
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-muted/50 border-t border-border mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="text-sm text-muted-foreground">
              © 2025 IKIA Conference. All rights reserved.
            </div>
            
            <div className="flex items-center space-x-6 text-sm">
              <a 
                href="#" 
                className="text-muted-foreground hover:text-primary-green transition-colors"
              >
                Privacy Policy
              </a>
              <a 
                href="#" 
                className="text-muted-foreground hover:text-primary-green transition-colors"
              >
                Terms & Conditions
              </a>
              <a 
                href="#" 
                className="text-muted-foreground hover:text-primary-green transition-colors"
              >
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
