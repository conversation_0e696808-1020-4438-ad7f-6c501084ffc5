'use client'

import React from 'react'
import { useRegistration } from '@/context/RegistrationContext'
import { GuestRegistrationForm } from './GuestRegistrationForm'
import { ExhibitorRegistrationForm } from './ExhibitorRegistrationForm'
import { InvestorRegistrationForm } from './InvestorRegistrationForm'
import { VIPRegistrationForm } from './VIPRegistrationForm'
import { SponsorRegistrationForm } from './SponsorRegistrationForm'



export function RegistrationFormRouter() {
  const { state } = useRegistration()

  if (!state.data.userType) {
    return (
      <div className="max-w-4xl mx-auto text-center space-y-4">
        <h1 className="text-3xl font-bold text-destructive">Error</h1>
        <p className="text-muted-foreground">
          No user type selected. Please go back and select a registration type.
        </p>
      </div>
    )
  }

  switch (state.data.userType) {
    case 'guest':
      return <GuestRegistrationForm />
    
    case 'exhibitor':
      return <ExhibitorRegistrationForm />
    
    case 'investor':
      return <InvestorRegistrationForm />
    
    case 'vip':
      return <VIPRegistrationForm />

    case 'sponsor':
      return <SponsorRegistrationForm />
    
    default:
      return (
        <div className="max-w-4xl mx-auto text-center space-y-4">
          <h1 className="text-3xl font-bold text-destructive">Error</h1>
          <p className="text-muted-foreground">
            Unknown registration type: {state.data.userType}
          </p>
        </div>
      )
  }
}
