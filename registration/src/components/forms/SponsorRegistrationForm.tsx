'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Select } from '@/components/ui/Select'
import { Textarea } from '@/components/ui/Textarea'
import { Checkbox } from '@/components/ui/Checkbox'
import { Button } from '@/components/ui/Button'
import { useRegistration } from '@/context/RegistrationContext'
import { COUNTRIES, INDUSTRIES, SPONSORSHIP_TIERS, PAYMENT_METHODS } from '@/lib/constants'
import { SponsorRegistrationData } from '@/types/registration'
import { formatCurrency } from '@/lib/utils'
import { 
  Handshake, 
  User, 
  Mail, 
  Phone, 
  Globe, 
  Building, 
  Briefcase, 
  Upload, 
  CreditCard,
  Crown,
  Award,
  Star,
  CheckCircle,
  Zap,
  Target
} from 'lucide-react'

const sponsorSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phoneNumber: z.string().min(10, 'Please enter a valid phone number'),
  country: z.string().min(1, 'Please select your country'),
  jobTitle: z.string().min(2, 'Job title is required'),
  companyName: z.string().min(2, 'Company name is required'),
  companyWebsite: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  companyDescription: z.string().min(100, 'Company description must be at least 100 characters').max(1000, 'Company description must be less than 1000 characters'),
  companyIndustry: z.string().min(1, 'Please select your company industry'),
  companyCountry: z.string().min(1, 'Please select your company headquarters country'),
  sponsorshipTier: z.enum(['platinum', 'gold', 'silver', 'bronze'], { required_error: 'Please select a sponsorship tier' }),
  paymentMethod: z.string().min(1, 'Please select your preferred payment method'),
  specialRequests: z.string().optional(),
  agreedToTerms: z.boolean().refine(val => val === true, 'You must agree to the sponsorship terms and conditions'),
})

type SponsorFormData = z.infer<typeof sponsorSchema>

export function SponsorRegistrationForm() {
  const { state, updateData, nextStep } = useRegistration()
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    trigger
  } = useForm<SponsorFormData>({
    resolver: zodResolver(sponsorSchema),
    defaultValues: {
      fullName: state.data.fullName || '',
      email: state.data.email || '',
      phoneNumber: state.data.phoneNumber || '',
      country: state.data.country || '',
      jobTitle: (state.data as SponsorRegistrationData)?.jobTitle || '',
      companyName: (state.data as SponsorRegistrationData)?.companyName || '',
      companyWebsite: (state.data as SponsorRegistrationData)?.companyWebsite || '',
      companyDescription: (state.data as SponsorRegistrationData)?.companyDescription || '',
      companyIndustry: (state.data as SponsorRegistrationData)?.companyIndustry || '',
      companyCountry: (state.data as SponsorRegistrationData)?.companyCountry || '',
      sponsorshipTier: (state.data as SponsorRegistrationData)?.sponsorshipTier || 'gold',
      paymentMethod: (state.data as SponsorRegistrationData)?.paymentMethod || '',
      specialRequests: (state.data as SponsorRegistrationData)?.specialRequests || '',
      agreedToTerms: (state.data as SponsorRegistrationData)?.agreedToTerms || false,
    }
  })

  const watchedSponsorshipTier = watch('sponsorshipTier')
  const selectedTier = SPONSORSHIP_TIERS.find(tier => tier.id === watchedSponsorshipTier)

  const onSubmit = (data: SponsorFormData) => {
    const registrationData: Partial<SponsorRegistrationData> = {
      userType: 'sponsor',
      ...data,
    }
    updateData(registrationData)
    nextStep()
  }

  const getTierIcon = (tierId: string) => {
    switch (tierId) {
      case 'platinum': return <Crown className="w-6 h-6" />
      case 'gold': return <Award className="w-6 h-6" />
      case 'silver': return <Star className="w-6 h-6" />
      case 'bronze': return <Target className="w-6 h-6" />
      default: return <Handshake className="w-6 h-6" />
    }
  }

  const getTierGradient = (tierId: string) => {
    switch (tierId) {
      case 'platinum': return 'from-slate-400 to-slate-600'
      case 'gold': return 'from-yellow-400 to-yellow-600'
      case 'silver': return 'from-gray-300 to-gray-500'
      case 'bronze': return 'from-amber-600 to-amber-800'
      default: return 'from-primary-green to-primary-yellow'
    }
  }

  return (
    <div className="max-w-5xl mx-auto space-y-8">
      {/* Premium Header Design */}
      <div className="text-center space-y-6">
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-primary-green via-primary-yellow to-secondary-sienna rounded-full shadow-lg">
          <Handshake className="w-12 h-12 text-white" />
        </div>
        
        <div className="space-y-4">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary-brown via-primary-green to-primary-yellow bg-clip-text text-transparent">
            Sponsor Partnership
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Join us as a valued partner in advancing Indigenous Knowledge Intellectual Assets. 
            Your sponsorship drives innovation and creates lasting impact in communities worldwide.
          </p>
        </div>

        {/* Impact Statement */}
        <div className="bg-gradient-to-r from-primary-green/10 via-primary-yellow/10 to-secondary-sienna/10 rounded-2xl p-6 border border-primary-green/20">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Zap className="w-5 h-5 text-primary-yellow" />
            <span className="text-sm font-semibold text-primary-brown">PARTNERSHIP IMPACT</span>
            <Zap className="w-5 h-5 text-primary-yellow" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-green">500+</div>
              <div className="text-muted-foreground">Expected Attendees</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-yellow">50+</div>
              <div className="text-muted-foreground">Countries Represented</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-sienna">$10M+</div>
              <div className="text-muted-foreground">Investment Potential</div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Contact Information */}
        <Card className="border-l-4 border-l-primary-green shadow-soft hover:shadow-medium transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-primary-green/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-green rounded-lg flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Contact Information</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Primary contact for sponsorship coordination
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Contact person's full name"
                leftIcon={<User className="w-4 h-4" />}
                error={errors.fullName?.message}
                required
                {...register('fullName')}
              />
              
              <Input
                label="Job Title"
                placeholder="Your position in the company"
                leftIcon={<Briefcase className="w-4 h-4" />}
                error={errors.jobTitle?.message}
                required
                {...register('jobTitle')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
                error={errors.email?.message}
                required
                {...register('email')}
              />
              
              <Input
                label="Phone Number"
                type="tel"
                placeholder="+****************"
                leftIcon={<Phone className="w-4 h-4" />}
                error={errors.phoneNumber?.message}
                required
                {...register('phoneNumber')}
              />
            </div>

            <Select
              label="Country"
              placeholder="Select your country"
              options={COUNTRIES.map(country => ({
                value: country.code,
                label: `${country.flag} ${country.name}`
              }))}
              error={errors.country?.message}
              required
              {...register('country')}
            />
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card className="border-l-4 border-l-primary-yellow shadow-soft hover:shadow-medium transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-primary-yellow/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-yellow rounded-lg flex items-center justify-center">
                <Building className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Company Information</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Tell us about your organization
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Company Name"
                placeholder="Your company name"
                leftIcon={<Building className="w-4 h-4" />}
                error={errors.companyName?.message}
                required
                {...register('companyName')}
              />
              
              <Input
                label="Company Website"
                type="url"
                placeholder="https://www.yourcompany.com"
                leftIcon={<Globe className="w-4 h-4" />}
                error={errors.companyWebsite?.message}
                helpText="Optional but recommended for marketing materials"
                {...register('companyWebsite')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Select
                label="Company Industry"
                placeholder="Select your industry"
                options={INDUSTRIES.map(industry => ({
                  value: industry.value,
                  label: industry.label
                }))}
                error={errors.companyIndustry?.message}
                required
                {...register('companyIndustry')}
              />
              
              <Select
                label="Company Headquarters"
                placeholder="Select headquarters country"
                options={COUNTRIES.map(country => ({
                  value: country.code,
                  label: `${country.flag} ${country.name}`
                }))}
                error={errors.companyCountry?.message}
                required
                {...register('companyCountry')}
              />
            </div>

            <Textarea
              label="Company Description"
              placeholder="Describe your company, its mission, and interest in supporting Indigenous Knowledge Intellectual Assets..."
              rows={4}
              maxLength={1000}
              error={errors.companyDescription?.message}
              helpText="This will be used in promotional materials and sponsor recognition (100-1000 characters)"
              required
              {...register('companyDescription')}
            />
          </CardContent>
        </Card>

        {/* Sponsorship Tier Selection - Premium Design */}
        <Card className="border-l-4 border-l-secondary-sienna shadow-soft hover:shadow-medium transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-secondary-sienna/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-secondary-sienna rounded-lg flex items-center justify-center">
                <Award className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Sponsorship Investment</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Choose your partnership level and impact
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {SPONSORSHIP_TIERS.map((tier) => (
                <div
                  key={tier.id}
                  className={`relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-300 ${
                    watchedSponsorshipTier === tier.id
                      ? 'border-primary-green bg-primary-green/5 shadow-medium'
                      : 'border-border hover:border-primary-green/50 hover:shadow-soft'
                  } ${tier.highlighted ? 'ring-2 ring-primary-yellow ring-opacity-50' : ''}`}
                  onClick={() => setValue('sponsorshipTier', tier.id)}
                >
                  {tier.highlighted && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary-yellow text-white px-3 py-1 rounded-full text-xs font-semibold">
                        MOST POPULAR
                      </span>
                    </div>
                  )}
                  
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`w-12 h-12 bg-gradient-to-br ${getTierGradient(tier.id)} rounded-lg flex items-center justify-center text-white`}>
                        {getTierIcon(tier.id)}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-foreground">{tier.name}</h3>
                        <p className="text-2xl font-bold text-primary-green">
                          {formatCurrency(tier.amount, tier.currency)}
                        </p>
                      </div>
                    </div>
                    
                    {watchedSponsorshipTier === tier.id && (
                      <CheckCircle className="w-6 h-6 text-primary-green" />
                    )}
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-foreground mb-2">Key Benefits:</h4>
                    {tier.benefits.slice(0, 3).map((benefit, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-primary-green mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{benefit}</span>
                      </div>
                    ))}
                    {tier.benefits.length > 3 && (
                      <p className="text-xs text-muted-foreground italic">
                        + {tier.benefits.length - 3} more exclusive benefits
                      </p>
                    )}
                  </div>

                  <input
                    type="radio"
                    value={tier.id}
                    {...register('sponsorshipTier')}
                    className="sr-only"
                  />
                </div>
              ))}
            </div>
            
            {errors.sponsorshipTier && (
              <p className="text-sm text-destructive font-medium">
                {errors.sponsorshipTier.message}
              </p>
            )}

            {/* Selected Tier Summary */}
            {selectedTier && (
              <div className="bg-gradient-to-r from-primary-green/5 to-primary-yellow/5 rounded-lg p-4 border border-primary-green/20">
                <h4 className="font-semibold text-foreground mb-2">
                  Selected: {selectedTier.name} - {formatCurrency(selectedTier.amount, selectedTier.currency)}
                </h4>
                <p className="text-sm text-muted-foreground">
                  Your investment includes {selectedTier.benefits.length} exclusive benefits and recognition opportunities.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment & Additional Information */}
        <Card className="border-l-4 border-l-secondary-blue shadow-soft hover:shadow-medium transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-secondary-blue/5 to-transparent">
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-secondary-blue rounded-lg flex items-center justify-center">
                <CreditCard className="w-5 h-5 text-white" />
              </div>
              <div>
                <span className="text-xl">Payment & Special Requests</span>
                <p className="text-sm text-muted-foreground font-normal mt-1">
                  Payment preferences and additional requirements
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <Select
              label="Preferred Payment Method"
              placeholder="Select payment method"
              options={PAYMENT_METHODS}
              error={errors.paymentMethod?.message}
              helpText="Final payment processing will be coordinated separately"
              required
              {...register('paymentMethod')}
            />

            <Textarea
              label="Special Requests or Questions"
              placeholder="Any specific requests regarding sponsorship benefits, branding opportunities, or questions about the partnership..."
              rows={4}
              helpText="Our team will review and respond to all special requests"
              {...register('specialRequests')}
            />

            {/* File Upload Section */}
            <div className="space-y-4">
              <label className="text-sm font-medium leading-none">
                Company Materials
              </label>
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center hover:border-primary-green/50 transition-colors">
                <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-1">
                  Upload your company logo and marketing materials
                </p>
                <p className="text-xs text-muted-foreground mb-3">
                  PNG, JPG, PDF up to 10MB each
                </p>
                <Button type="button" variant="outline" size="sm">
                  Choose Files
                </Button>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="bg-muted/30 rounded-lg p-4">
              <Checkbox
                label="I agree to the Sponsorship Terms & Conditions"
                description="By checking this box, you agree to our sponsorship terms, conditions, and partnership agreement"
                checked={watch('agreedToTerms')}
                onChange={(e) => setValue('agreedToTerms', e.target.checked)}
                error={errors.agreedToTerms?.message}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button - Premium Design */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button
            type="submit"
            size="lg"
            loading={isSubmitting}
            disabled={!watch('agreedToTerms')}
            className="min-w-[280px] bg-gradient-to-r from-primary-green via-primary-yellow to-secondary-sienna hover:from-primary-green/90 hover:via-primary-yellow/90 hover:to-secondary-sienna/90 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <Handshake className="w-5 h-5 mr-2" />
            Submit Sponsorship Application
          </Button>
        </div>
      </form>
    </div>
  )
}
